import re  

def clean_file(input_file, output_file, encoding='utf-8'):  
    try:  
        with open(input_file, 'r', encoding=encoding) as infile, \
                open(output_file, 'w', encoding='utf-8') as outfile:  
            for i, line in enumerate(infile):  
                try:  
                    # 尝试解码每一行  
                    line.encode('utf-8').decode('utf-8')  
                    # 删除控制字符和不可见字符  
                    cleaned_line = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', line)  
                    outfile.write(cleaned_line)  
                except UnicodeDecodeError as e:  
                    print(f"编码错误发生在第 {i+1} 行：")  
                    print(f"  {line.strip()}")  
                    print(f"  错误信息：{e}")  
                    return  # 遇到错误就停止  
            print(f"已清理文件：{output_file}")  
    except FileNotFoundError:  
        print(f"错误：文件 {input_file} 未找到")  
    except Exception as e:  
        print(f"发生错误：{e}")  

input_file = '/Users/<USER>/Downloads/A_Couples_Duet-0.14.5-pc/game/tl/chinese/scenes/28.clubaa.rpy'  
output_file = '/Users/<USER>/Downloads/A_Couples_Duet-0.14.5-pc/game/tl/chinese/scenes/28.clubaa_cleaned.rpy'  

clean_file(input_file, output_file, encoding='utf-8')  