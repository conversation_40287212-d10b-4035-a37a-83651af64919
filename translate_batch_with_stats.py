#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import time
import argparse
from datetime import datetime
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.tmt.v20180321 import tmt_client, models

def translate_text(client, text, source="en", target="zh"):
    """
    使用腾讯云翻译API翻译文本
    
    Args:
        client: 腾讯云翻译客户端
        text: 待翻译文本
        source: 源语言，默认为英语
        target: 目标语言，默认为中文
        
    Returns:
        翻译后的文本，失败返回None
    """
    try:
        # 创建请求对象
        req = models.TextTranslateRequest()
        
        # 设置请求参数
        req.SourceText = text
        req.Source = source
        req.Target = target
        req.ProjectId = 0
        
        # 发送请求
        resp = client.TextTranslate(req)
        
        # 返回翻译结果
        return resp.TargetText
    
    except TencentCloudSDKException as err:
        print(f"翻译失败: {err}")
        return None
    except Exception as e:
        print(f"请求异常: {str(e)}")
        return None

def translate_file(client, input_file, output_file, source="en", target="zh"):
    """
    翻译文件并按Ren'Py翻译格式输出
    
    Args:
        client: 腾讯云翻译客户端
        input_file: 输入文件路径
        output_file: 输出文件路径
        source: 源语言
        target: 目标语言
    
    Returns:
        成功翻译的行数, 总字符数, 翻译耗时(秒)
    """
    try:
        start_time = time.time()
        
        # 读取文件内容
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 过滤空行，但保留空行的位置信息
        filtered_lines = []
        total_chars = 0
        
        for i, line in enumerate(lines):
            line = line.strip()
            if line:  # 只处理非空行
                filtered_lines.append(line)
                total_chars += len(line)
        
        print(f"开始翻译文件: {input_file}")
        print(f"总行数: {len(filtered_lines)} (非空行)")
        print(f"总字符数: {total_chars}")
        
        # 准备翻译结果
        translated_lines = []
        success_count = 0
        
        # 逐行翻译
        for i, line in enumerate(filtered_lines):
            # 翻译非空行
            translated = translate_text(client, line, source, target)
            
            # 添加延迟，避免请求过快
            time.sleep(0.5)
            
            if translated:
                translated_lines.append((line, translated))
                success_count += 1
            else:
                # 翻译失败，保留原文
                translated_lines.append((line, line))
            
            # 显示进度
            if (i + 1) % 10 == 0 or i == len(filtered_lines) - 1:
                print(f"进度: {i+1}/{len(filtered_lines)} ({(i+1)/len(filtered_lines)*100:.1f}%)")
        
        # 保存翻译结果为Ren'Py格式
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            # 写入Ren'Py翻译头
            f.write("translate chinese strings:\n")
            
            # 写入每个翻译条目
            for i, (original, translated) in enumerate(translated_lines):
                f.write(f'    old "{original}"\n')
                f.write(f'    new "{translated}"\n')
                
                # 每个条目之间空一行，但最后一个条目后不空行
                if i < len(translated_lines) - 1:
                    f.write("\n")
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"翻译完成: {success_count}/{len(filtered_lines)} 行已翻译")
        print(f"耗时: {elapsed_time:.2f} 秒")
        
        return success_count, total_chars, elapsed_time
    
    except Exception as e:
        print(f"翻译文件 {input_file} 时出错: {str(e)}")
        return 0, 0, 0

def batch_translate(client, input_folder, output_folder, source="en", target="zh", limit=10):
    """
    批量翻译文件夹中的文件
    
    Args:
        client: 腾讯云翻译客户端
        input_folder: 输入文件夹路径
        output_folder: 输出文件夹路径
        source: 源语言
        target: 目标语言
        limit: 限制处理的文件数量
    """
    # 创建输出文件夹
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"创建输出文件夹: {output_folder}")
    
    # 获取所有文本文件
    txt_files = []
    for root, _, files in os.walk(input_folder):
        for file in files:
            if file.endswith(".txt"):
                txt_files.append(os.path.join(root, file))
    
    if not txt_files:
        print(f"在 {input_folder} 中没有找到.txt文件")
        return
    
    # 按文件名排序
    txt_files.sort()
    
    # 如果有限制，只处理指定数量的文件
    if limit > 0:
        txt_files = txt_files[:limit]
    
    print(f"将处理 {len(txt_files)} 个文件")
    
    # 准备统计信息
    stats = []
    total_start_time = time.time()
    
    # 处理每个文件
    for file_path in txt_files:
        # 获取文件名（不含路径和后缀）
        file_name = os.path.basename(file_path)
        file_name_without_ext = os.path.splitext(file_name)[0]
        
        # 设置输出文件路径，改为.rpy后缀
        output_file = os.path.join(output_folder, f"{file_name_without_ext}.rpy")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 翻译文件
        lines, chars, elapsed = translate_file(client, file_path, output_file, source, target)
        
        # 记录统计信息
        stats.append({
            "file_name": file_name,
            "lines": lines,
            "chars": chars,
            "elapsed_seconds": elapsed,
            "chars_per_second": chars / elapsed if elapsed > 0 else 0
        })
        
        print("-" * 50)
    
    total_elapsed = time.time() - total_start_time
    total_lines = sum(s["lines"] for s in stats)
    total_chars = sum(s["chars"] for s in stats)
    
    # 打印统计信息
    print("\n翻译统计:")
    print(f"总文件数: {len(stats)}")
    print(f"总行数: {total_lines}")
    print(f"总字符数: {total_chars}")
    print(f"总耗时: {total_elapsed:.2f} 秒")
    print(f"平均速度: {total_chars/total_elapsed:.2f} 字符/秒")
    
    print("\n各文件统计:")
    print(f"{'文件名':<30} {'行数':>8} {'字符数':>10} {'耗时(秒)':>12} {'速度(字符/秒)':>15}")
    print("-" * 80)
    for s in stats:
        print(f"{s['file_name']:<30} {s['lines']:>8} {s['chars']:>10} {s['elapsed_seconds']:>12.2f} {s['chars_per_second']:>15.2f}")
    
    # 保存统计信息到文件
    stats_file = os.path.join(output_folder, "translation_stats.json")
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "total_files": len(stats),
            "total_lines": total_lines,
            "total_chars": total_chars,
            "total_elapsed_seconds": total_elapsed,
            "avg_chars_per_second": total_chars/total_elapsed if total_elapsed > 0 else 0,
            "files": stats
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n统计信息已保存到: {stats_file}")

def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='使用腾讯云翻译API批量翻译文本文件')
    parser.add_argument('--secret_id', required=True, help='腾讯云API密钥ID')
    parser.add_argument('--secret_key', required=True, help='腾讯云API密钥Key')
    parser.add_argument('--region', default='ap-guangzhou', help='腾讯云地域，默认为广州')
    parser.add_argument('--input_folder', default='extracted_quotes', help='输入文件夹路径')
    parser.add_argument('--output_folder', default='/Users/<USER>/Downloads/A_Couples_Duet-0.14.5-pc/game/tl/chinese/scenes', help='输出文件夹路径')
    parser.add_argument('--source', default='en', help='源语言，默认为英语')
    parser.add_argument('--target', default='zh', help='目标语言，默认为中文')
    parser.add_argument('--limit', type=int, default=10, help='限制处理的文件数量，默认为10')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    try:
        # 实例化一个认证对象
        cred = credential.Credential(args.secret_id, args.secret_key)
        
        # 实例化一个http选项，可选的，没有特殊需求可以跳过
        httpProfile = HttpProfile()
        httpProfile.endpoint = "tmt.tencentcloudapi.com"
        
        # 实例化一个client选项，可选的，没有特殊需求可以跳过
        clientProfile = ClientProfile()
        clientProfile.httpProfile = httpProfile
        
        # 实例化要请求产品的client对象
        client = tmt_client.TmtClient(cred, args.region, clientProfile)
        
        # 测试连接
        print("测试API连接...")
        test_result = translate_text(client, "Hello world", args.source, args.target)
        if test_result:
            print(f"API连接测试成功! 翻译结果: {test_result}")
            
            # 批量翻译
            batch_translate(client, args.input_folder, args.output_folder, args.source, args.target, args.limit)
        else:
            print("API连接测试失败，请检查密钥和地域设置")
        
    except TencentCloudSDKException as err:
        print(f"腾讯云SDK异常: {err}")
    except Exception as e:
        print(f"程序异常: {str(e)}")

if __name__ == "__main__":
    main()
