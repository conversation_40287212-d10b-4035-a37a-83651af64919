#!/bin/bash
# 使用原生Ren'Py引擎启动游戏

echo "🎮 启动 A Coup<PERSON>'s Duet 游戏 (原生Ren'Py)"
echo "========================================="

# 检查Linux版本的Ren'Py引擎
RENPY_PYTHON="lib/py3-linux-x86_64/python"

if [ ! -f "$RENPY_PYTHON" ]; then
    echo "❌ Ren'Py Linux引擎未找到: $RENPY_PYTHON"
    exit 1
fi

# 检查游戏目录
if [ ! -d "game" ]; then
    echo "❌ 游戏目录不存在"
    exit 1
fi

# 检查翻译文件
TRANSLATION_DIR="game/tl/chinese"
if [ ! -d "$TRANSLATION_DIR" ]; then
    echo "❌ 翻译目录不存在: $TRANSLATION_DIR"
    exit 1
fi

echo "✅ Ren'Py引擎: $RENPY_PYTHON"
echo "✅ 游戏目录: game/"
echo "✅ 翻译目录: $TRANSLATION_DIR"
echo ""

# 设置环境变量
export RENPY_PLATFORM="linux-x86_64"
export PYTHONPATH="lib/python3.9:$PYTHONPATH"

# 启动游戏
echo "🚀 启动游戏..."
echo "📋 执行命令: $RENPY_PYTHON -O renpy/bootstrap.py ."
echo ""

# 使用Ren'Py的Python解释器启动游戏
"$RENPY_PYTHON" -O renpy/bootstrap.py . 2>&1

EXIT_CODE=$?
echo ""

if [ $EXIT_CODE -eq 0 ]; then
    echo "🎮 游戏正常退出"
else
    echo "❌ 游戏启动失败 (退出代码: $EXIT_CODE)"
    echo ""
    echo "💡 可能的解决方案:"
    echo "1. 检查是否缺少依赖库"
    echo "2. 确认系统支持运行Linux二进制文件"
    echo "3. 尝试使用Wine方案"
fi
