#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import httpx
import time
import os
import re
from datetime import datetime

def translate_text(text, source_lang="EN", target_lang="ZH", max_retries=3, retry_delay=2):
    """
    使用DeepLX API翻译文本，带重试机制

    Args:
        text: 要翻译的文本
        source_lang: 源语言代码，默认为英文(EN)
        target_lang: 目标语言代码，默认为中文(ZH)
        max_retries: 最大重试次数
        retry_delay: 重试间隔（秒）

    Returns:
        包含翻译结果的字典
    """
    if not text.strip():
        return {
            "success": True,
            "translated_text": "",
            "message": "Empty text",
            "attempts": 0
        }

    data = {
        "text": text,
        "source_lang": source_lang,
        "target_lang": target_lang
    }

    print(f"  准备翻译文本: {text[:30]}...")

    for attempt in range(max_retries):
        try:
            print(f"  尝试 {attempt + 1}/{max_retries} 发送请求...")
            response = httpx.post(
                "http://localhost:1188/translate",
                headers={"Content-Type": "application/json"},
                json=data,
                timeout=30
            )

            print(f"  收到响应状态码: {response.status_code}")

            try:
                result = response.json()
                print(f"  响应内容: {result}")
            except Exception as e:
                print(f"  解析JSON响应失败: {str(e)}")
                print(f"  原始响应内容: {response.text}")
                result = {"code": 0, "message": f"Failed to parse response: {str(e)}"}

            if result.get("code") == 200:
                translated_text = result.get("data", "")
                print(f"  翻译成功: {translated_text[:30]}...")
                return {
                    "success": True,
                    "translated_text": translated_text,
                    "message": "Success",
                    "attempts": attempt + 1
                }
            else:
                # 如果不是最后一次尝试，则等待后重试
                if attempt < max_retries - 1:
                    print(f"  尝试 {attempt + 1}/{max_retries} 失败: {result.get('message', '未知错误')}，等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                else:
                    return {
                        "success": False,
                        "translated_text": "",
                        "message": f"API错误: {result.get('message', 'Unknown error')} (代码: {result.get('code', 'Unknown')})",
                        "attempts": attempt + 1
                    }
        except Exception as e:
            # 如果不是最后一次尝试，则等待后重试
            if attempt < max_retries - 1:
                print(f"  尝试 {attempt + 1}/{max_retries} 异常: {str(e)}，等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                return {
                    "success": False,
                    "translated_text": "",
                    "message": str(e),
                    "attempts": attempt + 1
                }

    # 如果所有重试都失败
    return {
        "success": False,
        "translated_text": "",
        "message": "All retry attempts failed",
        "attempts": max_retries
    }

def main():
    # 读取源文件
    source_file = "/Users/<USER>/Downloads/A_Couples_Duet-0.14.5-pc/extracted_quotes/4.jensam_firstvisit.txt"
    output_file = "/Users/<USER>/Downloads/A_Couples_Duet-0.14.5-pc/game/tl/chinese/scenes/4.jensam_firstvisit.rpy"

    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    print(f"读取源文件: {source_file}")
    try:
        with open(source_file, 'r', encoding='utf-8') as f:
            lines = f.read().splitlines()
    except Exception as e:
        print(f"读取文件失败: {str(e)}")
        return

    total_lines = len(lines)
    print(f"共读取到 {total_lines} 行文本")

    # 翻译每一行
    print("\n开始翻译...")
    start_time = time.time()
    translated_results = []
    success_count = 0
    total_attempts = 0

    for i, line in enumerate(lines):
        print(f"翻译第 {i+1}/{total_lines} 行...")

        # 跳过空行和特殊行（如音效行）
        if not line.strip() or line.startswith("sounds/"):
            translated_results.append({
                "original": line,
                "translated": line,  # 保持原样
                "success": True,
                "attempts": 0
            })
            success_count += 1
            continue

        result = translate_text(line)

        translated_results.append({
            "original": line,
            "translated": result.get("translated_text", ""),
            "success": result.get("success", False),
            "message": result.get("message", ""),
            "attempts": result.get("attempts", 0)
        })

        if result.get("success"):
            success_count += 1
            if result.get("attempts", 0) > 1:
                print(f"  成功! (尝试次数: {result.get('attempts')})")
        else:
            print(f"  失败: {result.get('message', '未知错误')} (尝试次数: {result.get('attempts', 0)})")

        total_attempts += result.get("attempts", 0)

        # 短暂暂停，避免请求过于频繁
        time.sleep(0.5)

    # 计算总耗时和完成度
    elapsed_time = time.time() - start_time
    completion_rate = (success_count / total_lines) * 100 if total_lines > 0 else 0
    avg_attempts = total_attempts / total_lines if total_lines > 0 else 0

    # 生成rpy格式的输出文件
    print("\n生成输出文件...")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("translate chinese strings:\n")

        for item in translated_results:
            if item["original"].strip():  # 非空行
                if item["original"].startswith("sounds/"):
                    # 音效行直接保持原样
                    f.write(f"    {item['original']}\n")
                else:
                    # 普通文本行按照格式转换
                    f.write(f"    old \"{item['original']}\"\n")
                    if item['success']:
                        f.write(f"    new \"{item['translated']}\"\n")
                    else:
                        f.write(f"    new \"{item['original']}\"\n")  # 翻译失败时保持原文
                    f.write("\n")
            else:
                # 空行保持原样
                f.write("\n")

    # 打印摘要
    print("\n" + "=" * 50)
    print(f"翻译完成!")
    print(f"成功率: {completion_rate:.2f}% ({success_count}/{total_lines})")
    print(f"总耗时: {elapsed_time:.2f} 秒")
    print(f"平均尝试次数: {avg_attempts:.2f}")
    print(f"结果已保存到: {output_file}")
    print("=" * 50)

if __name__ == "__main__":
    main()
