#!/bin/bash
# Wine中文字体安装脚本

echo "🔤 安装Wine中文字体支持"
echo "========================"

# 设置Wine环境
export WINEPREFIX="$HOME/.wine"
export WINEARCH=win64

# 检查Wine是否安装
if ! command -v wine &> /dev/null; then
    echo "❌ Wine未安装，请先安装Wine"
    echo "   brew install --cask wine-stable"
    exit 1
fi

# 初始化Wine环境（如果需要）
if [ ! -d "$WINEPREFIX" ]; then
    echo "🔧 初始化Wine环境..."
    winecfg
fi

# 创建字体目录
FONT_DIR="$WINEPREFIX/drive_c/windows/Fonts"
mkdir -p "$FONT_DIR"

echo "📁 字体目录: $FONT_DIR"

# 复制系统中文字体
echo "📋 复制系统中文字体..."

# macOS系统字体路径
SYSTEM_FONTS="/System/Library/Fonts"
USER_FONTS="/Users/<USER>/Library/Fonts"

# 复制常用中文字体
for font_path in "$SYSTEM_FONTS" "$USER_FONTS"; do
    if [ -d "$font_path" ]; then
        # 复制中文字体
        find "$font_path" -name "*Chinese*" -type f -exec cp {} "$FONT_DIR/" \; 2>/dev/null
        find "$font_path" -name "*SimSun*" -type f -exec cp {} "$FONT_DIR/" \; 2>/dev/null
        find "$font_path" -name "*SimHei*" -type f -exec cp {} "$FONT_DIR/" \; 2>/dev/null
        find "$font_path" -name "*Microsoft*" -type f -exec cp {} "$FONT_DIR/" \; 2>/dev/null
        find "$font_path" -name "*PingFang*" -type f -exec cp {} "$FONT_DIR/" \; 2>/dev/null
        find "$font_path" -name "*Hiragino*" -type f -exec cp {} "$FONT_DIR/" \; 2>/dev/null
    fi
done

# 注册字体到Wine注册表
echo "📝 注册字体到Wine..."
wine regedit /s /dev/stdin << 'EOF'
Windows Registry Editor Version 5.00

[HKEY_LOCAL_MACHINE\Software\Microsoft\Windows NT\CurrentVersion\FontLink\SystemLink]
"Lucida Sans Unicode"="PingFang.ttc,PingFang SC"
"Microsoft Sans Serif"="PingFang.ttc,PingFang SC"
"MS Sans Serif"="PingFang.ttc,PingFang SC"
"Tahoma"="PingFang.ttc,PingFang SC"
"Tahoma Bold"="PingFang.ttc,PingFang SC"
"SimSun"="PingFang.ttc,PingFang SC"
"Arial"="PingFang.ttc,PingFang SC"
"Arial Bold"="PingFang.ttc,PingFang SC"
EOF

echo "✅ 中文字体安装完成"
echo ""
echo "💡 提示:"
echo "   - 如果游戏中文显示仍有问题，请运行 winecfg"
echo "   - 在Graphics选项卡中调整DPI设置"
echo "   - 在Fonts选项卡中设置字体替换"
