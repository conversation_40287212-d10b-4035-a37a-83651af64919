#!/bin/bash
# Ren'Py游戏Wine启动脚本

echo "🎮 启动 A Couple's Duet 游戏"
echo "================================"

# 设置Wine环境
export WINEPREFIX="$HOME/.wine"
export WINEARCH=win64

# 设置Wine路径
WINE_PATH="/Applications/Wine Stable.app/Contents/Resources/wine/bin/wine"

# 检查Wine是否安装
if [ ! -f "$WINE_PATH" ]; then
    echo "❌ Wine未找到，请确认Wine已正确安装"
    echo "   预期路径: $WINE_PATH"
    exit 1
fi

# 检查游戏文件
GAME_EXE="../A_Couples_Duet.exe"
if [ ! -f "$GAME_EXE" ]; then
    echo "❌ 游戏文件不存在: $GAME_EXE"
    exit 1
fi

# 检查翻译文件
TRANSLATION_DIR="../game/tl/chinese"
if [ ! -d "$TRANSLATION_DIR" ]; then
    echo "❌ 翻译目录不存在: $TRANSLATION_DIR"
    exit 1
fi

echo "✅ 环境检查完成"
echo "📁 游戏文件: $GAME_EXE"
echo "📁 翻译目录: $TRANSLATION_DIR"
echo ""

# 检查Wine前缀是否存在
if [ ! -d "$WINEPREFIX" ]; then
    echo "⚠️  Wine环境未初始化，请先运行 ./setup_wine.sh"
    read -p "是否现在初始化Wine环境？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        ./setup_wine.sh
    else
        exit 1
    fi
fi

# 启动游戏
echo "🚀 启动游戏..."
cd ..

# 尝试启动游戏
echo "📋 使用Wine前缀: $WINEPREFIX"
echo "📋 启动命令: $WINE_PATH $GAME_EXE"
echo ""

"$WINE_PATH" "$GAME_EXE" 2>&1 | tee wine_launcher/game_output.log

EXIT_CODE=$?
echo ""

if [ $EXIT_CODE -eq 0 ]; then
    echo "🎮 游戏正常退出"
else
    echo "❌ 游戏启动失败 (退出代码: $EXIT_CODE)"
    echo "📋 错误日志已保存到: wine_launcher/game_output.log"
    echo ""
    echo "💡 故障排除建议:"
    echo "1. 运行 ./setup_wine.sh 重新初始化Wine环境"
    echo "2. 检查是否缺少运行时库"
    echo "3. 运行 winecfg 调整Wine设置"
fi
