[mvk-info] MoltenVK version 1.2.10, supporting Vulkan version 1.2.290.
	The following 110 Vulkan extensions are supported:
	VK_KHR_16bit_storage v1
	VK_KHR_8bit_storage v1
	VK_KHR_bind_memory2 v1
	VK_KHR_buffer_device_address v1
	VK_KHR_calibrated_timestamps v1
	VK_KHR_copy_commands2 v1
	VK_KHR_create_renderpass2 v1
	VK_KHR_dedicated_allocation v3
	VK_KHR_deferred_host_operations v4
	VK_KHR_depth_stencil_resolve v1
	VK_KHR_descriptor_update_template v1
	VK_KHR_device_group v4
	VK_KHR_device_group_creation v1
	VK_KHR_driver_properties v1
	VK_KHR_dynamic_rendering v1
	VK_KHR_external_fence v1
	VK_KHR_external_fence_capabilities v1
	VK_KHR_external_memory v1
	VK_KHR_external_memory_capabilities v1
	VK_KHR_external_semaphore v1
	VK_KHR_external_semaphore_capabilities v1
	VK_KHR_fragment_shader_barycentric v1
	VK_KHR_format_feature_flags2 v2
	VK_KHR_get_memory_requirements2 v1
	VK_KHR_get_physical_device_properties2 v2
	VK_KHR_get_surface_capabilities2 v1
	VK_KHR_imageless_framebuffer v1
	VK_KHR_image_format_list v1
	VK_KHR_incremental_present v2
	VK_KHR_maintenance1 v2
	VK_KHR_maintenance2 v1
	VK_KHR_maintenance3 v1
	VK_KHR_map_memory2 v1
	VK_KHR_multiview v1
	VK_KHR_portability_subset v1
	VK_KHR_push_descriptor v2
	VK_KHR_relaxed_block_layout v1
	VK_KHR_sampler_mirror_clamp_to_edge v3
	VK_KHR_sampler_ycbcr_conversion v14
	VK_KHR_separate_depth_stencil_layouts v1
	VK_KHR_shader_draw_parameters v1
	VK_KHR_shader_float_controls v4
	VK_KHR_shader_float16_int8 v1
	VK_KHR_shader_integer_dot_product v1
	VK_KHR_shader_non_semantic_info v1
	VK_KHR_shader_subgroup_extended_types v1
	VK_KHR_spirv_1_4 v1
	VK_KHR_storage_buffer_storage_class v1
	VK_KHR_surface v25
	VK_KHR_swapchain v70
	VK_KHR_swapchain_mutable_format v1
	VK_KHR_synchronization2 v1
	VK_KHR_timeline_semaphore v2
	VK_KHR_uniform_buffer_standard_layout v1
	VK_KHR_variable_pointers v1
	VK_KHR_vertex_attribute_divisor v1
	VK_EXT_4444_formats v1
	VK_EXT_buffer_device_address v2
	VK_EXT_calibrated_timestamps v2
	VK_EXT_debug_marker v4
	VK_EXT_debug_report v10
	VK_EXT_debug_utils v2
	VK_EXT_descriptor_indexing v2
	VK_EXT_extended_dynamic_state v1
	VK_EXT_extended_dynamic_state2 v1
	VK_EXT_extended_dynamic_state3 v2
	VK_EXT_external_memory_host v1
	VK_EXT_fragment_shader_interlock v1
	VK_EXT_hdr_metadata v3
	VK_EXT_headless_surface v1
	VK_EXT_host_image_copy v1
	VK_EXT_host_query_reset v1
	VK_EXT_image_robustness v1
	VK_EXT_inline_uniform_block v1
	VK_EXT_layer_settings v2
	VK_EXT_memory_budget v1
	VK_EXT_metal_objects v2
	VK_EXT_metal_surface v1
	VK_EXT_pipeline_creation_cache_control v3
	VK_EXT_pipeline_creation_feedback v1
	VK_EXT_post_depth_coverage v1
	VK_EXT_private_data v1
	VK_EXT_robustness2 v1
	VK_EXT_sample_locations v1
	VK_EXT_scalar_block_layout v1
	VK_EXT_separate_stencil_usage v1
	VK_EXT_shader_atomic_float v1
	VK_EXT_shader_demote_to_helper_invocation v1
	VK_EXT_shader_stencil_export v1
	VK_EXT_shader_subgroup_ballot v1
	VK_EXT_shader_subgroup_vote v1
	VK_EXT_shader_viewport_index_layer v1
	VK_EXT_subgroup_size_control v2
	VK_EXT_surface_maintenance1 v1
	VK_EXT_swapchain_colorspace v5
	VK_EXT_swapchain_maintenance1 v1
	VK_EXT_texel_buffer_alignment v1
	VK_EXT_texture_compression_astc_hdr v1
	VK_EXT_vertex_attribute_divisor v3
	VK_AMD_gpu_shader_half_float v2
	VK_AMD_negative_viewport_height v1
	VK_AMD_shader_image_load_store_lod v1
	VK_AMD_shader_trinary_minmax v1
	VK_IMG_format_pvrtc v1
	VK_INTEL_shader_integer_functions2 v1
	VK_GOOGLE_display_timing v1
	VK_MVK_macos_surface v3
	VK_MVK_moltenvk v37
	VK_NV_fragment_shader_barycentric v1
	VK_NV_glsl_shader v1
[mvk-info] GPU device:
	model: Apple M1 Pro
	type: Integrated
	vendorID: 0x106b
	deviceID: 0xe0603ef
	pipelineCacheUUID: EDBDCF05-0E06-03EF-0000-000000000000
	GPU memory available: 10922 MB
	GPU memory used: 0 MB
	Metal Shading Language 3.1
	supports the following GPU Features:
		GPU Family Metal 3
		GPU Family Apple 7
		GPU Family Apple 6
		GPU Family Apple 5
		GPU Family Apple 4
		GPU Family Apple 3
		GPU Family Apple 2
		GPU Family Apple 1
		GPU Family Mac 2
		GPU Family Mac 1
		GPU Family Common 3
		GPU Family Common 2
		GPU Family Common 1
		macOS GPU Family 2 v1
		macOS GPU Family 1 v4
		macOS GPU Family 1 v3
		macOS GPU Family 1 v2
		macOS GPU Family 1 v1
[mvk-info] Created VkInstance for Vulkan version 1.0.0, as requested by app, with the following 2 Vulkan extensions enabled:
	VK_KHR_external_memory_capabilities v1
	VK_KHR_get_physical_device_properties2 v2
0074:fixme:win:NtUserActivateKeyboardLayout Aliased keyboard layout not yet implemented
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b93fd10 (vid 05ac, pid 0342): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b91ca60 (vid 05ac, pid 8104): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b922450 (vid 05ac, pid 8104): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b96f890 (vid 05ac, pid 0342): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b922170 (vid 0000, pid 0000): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b91f3d0 (vid 05ac, pid 8104): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b948b50 (vid 05ac, pid 0342): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b921ad0 (vid 0000, pid 0000): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b91e1e0 (vid 05ac, pid 8104): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b9589e0 (vid 0000, pid 0000): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b944460 (vid 05ac, pid 0342): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b941380 (vid 05ac, pid 0342): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b9218b0 (vid 0000, pid 0000): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b930ee0 (vid 0000, pid 0000): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b91c410 (vid 0000, pid 0000): not a joystick or gamepad
00c4:err:hid:handle_DeviceMatchingCallback Ignoring HID device 0x7fdd1b931d70 (vid 05ac, pid 0342): not a joystick or gamepad
00d4:fixme:win:NtUserActivateKeyboardLayout Aliased keyboard layout not yet implemented
00b0:err:ntoskrnl:ZwLoadDriver failed to create driver L"\\Registry\\Machine\\System\\CurrentControlSet\\Services\\winebth": c00000e5
003c:fixme:service:scmdatabase_autostart_services Auto-start service L"winebth" failed to start: 1359
wine: failed to open "../A_Couples_Duet.exe": c0000135
