#!/bin/bash
# Wine环境初始化脚本

echo "🔧 初始化Wine环境用于Ren'Py游戏"
echo "================================"

# 设置Wine环境
export WINEPREFIX="$HOME/.wine_renpy"
export WINEARCH=win64

# 设置Wine路径
WINE_PATH="/Applications/Wine Stable.app/Contents/Resources/wine/bin/wine"
WINECFG_PATH="/Applications/Wine Stable.app/Contents/Resources/wine/bin/winecfg"
WINETRICKS_URL="https://raw.githubusercontent.com/Winetricks/winetricks/master/src/winetricks"

# 检查Wine是否安装
if [ ! -f "$WINE_PATH" ]; then
    echo "❌ Wine未找到，请确认Wine已正确安装"
    exit 1
fi

echo "✅ Wine路径: $WINE_PATH"

# 创建新的Wine前缀
if [ ! -d "$WINEPREFIX" ]; then
    echo "🔧 创建新的Wine前缀: $WINEPREFIX"
    "$WINE_PATH" wineboot --init
    
    # 等待初始化完成
    echo "⏳ 等待Wine初始化完成..."
    sleep 5
else
    echo "✅ Wine前缀已存在: $WINEPREFIX"
fi

# 下载并安装winetricks（如果需要）
if [ ! -f "/usr/local/bin/winetricks" ]; then
    echo "📥 下载winetricks..."
    curl -o /tmp/winetricks "$WINETRICKS_URL"
    chmod +x /tmp/winetricks
    sudo mv /tmp/winetricks /usr/local/bin/ 2>/dev/null || mv /tmp/winetricks ./winetricks
    WINETRICKS_CMD="./winetricks"
else
    WINETRICKS_CMD="winetricks"
fi

# 安装必要的运行时库
echo "📦 安装必要的运行时库..."

# 设置Wine为Windows 10模式
echo "🔧 设置Windows版本为Windows 10..."
"$WINE_PATH" winecfg /v win10

# 安装Visual C++ Redistributables
echo "📦 安装Visual C++ Redistributables..."
if command -v "$WINETRICKS_CMD" &> /dev/null; then
    WINEPREFIX="$WINEPREFIX" "$WINETRICKS_CMD" -q vcrun2019 vcrun2017 vcrun2015 vcrun2013 vcrun2012 vcrun2010 vcrun2008 vcrun2005
else
    echo "⚠️  winetricks未找到，跳过运行时库安装"
fi

# 安装.NET Framework
echo "📦 安装.NET Framework..."
if command -v "$WINETRICKS_CMD" &> /dev/null; then
    WINEPREFIX="$WINEPREFIX" "$WINETRICKS_CMD" -q dotnet48
else
    echo "⚠️  跳过.NET Framework安装"
fi

# 安装字体
echo "🔤 安装字体..."
if command -v "$WINETRICKS_CMD" &> /dev/null; then
    WINEPREFIX="$WINEPREFIX" "$WINETRICKS_CMD" -q corefonts
else
    echo "⚠️  跳过字体安装"
fi

# 复制中文字体
echo "🔤 复制中文字体..."
FONT_DIR="$WINEPREFIX/drive_c/windows/Fonts"
mkdir -p "$FONT_DIR"

# 从系统复制中文字体
for font_path in "/System/Library/Fonts" "/Users/<USER>/Library/Fonts"; do
    if [ -d "$font_path" ]; then
        find "$font_path" -name "*PingFang*" -type f -exec cp {} "$FONT_DIR/" \; 2>/dev/null
        find "$font_path" -name "*Hiragino*" -type f -exec cp {} "$FONT_DIR/" \; 2>/dev/null
        find "$font_path" -name "*Chinese*" -type f -exec cp {} "$FONT_DIR/" \; 2>/dev/null
    fi
done

echo "✅ Wine环境初始化完成！"
echo ""
echo "📝 下一步:"
echo "1. 运行 ./start_game.sh 启动游戏"
echo "2. 如果仍有问题，运行 winecfg 手动调整设置"
echo ""
echo "🔧 Wine前缀位置: $WINEPREFIX"
