#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def extract_quotes(file_path):
    """
    从Ren'Py脚本文件中提取所有引号内的文本，并去重

    Args:
        file_path: Ren'Py脚本文件的路径

    Returns:
        提取的不重复文本列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        # 如果UTF-8解码失败，尝试使用latin-1编码
        with open(file_path, 'r', encoding='latin-1') as f:
            content = f.read()

    # 使用正则表达式提取所有引号内的文本
    # 这个正则表达式匹配双引号内的文本，但不匹配转义的双引号
    pattern = r'(?<![\\])\"(.*?)(?<![\\])\"'
    quotes = re.findall(pattern, content)

    # 过滤掉空字符串
    quotes = [q for q in quotes if q.strip()]

    # 去重
    unique_quotes = []
    seen = set()
    for quote in quotes:
        if quote not in seen:
            seen.add(quote)
            unique_quotes.append(quote)

    return unique_quotes

def save_quotes(quotes, output_file):
    """
    将提取的文本保存到文件中，每行文本之间空一行

    Args:
        quotes: 提取的文本列表
        output_file: 输出文件的路径
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        for i, quote in enumerate(quotes):
            f.write(f"{quote}\n")
            # 每行之后空一行，但最后一行后不空行
            if i < len(quotes) - 1:
                f.write("\n")

def main():
    # 输入文件路径
    input_file = "/Users/<USER>/Downloads/A_Couples_Duet-0.14.5-pc/game/scenes/40.ethnat_s40.rpy"

    # 输出文件路径（在当前工作目录下）
    output_file = "scene40_unique_quotes.txt"

    # 提取引号内的文本并去重
    quotes = extract_quotes(input_file)

    # 保存提取的文本
    save_quotes(quotes, output_file)

    print(f"已从 {input_file} 中提取 {len(quotes)} 条不重复的文本")
    print(f"提取的文本已保存到 {output_file}")
    print(f"文本格式：每行一条文本，行与行之间空一行")

if __name__ == "__main__":
    main()
