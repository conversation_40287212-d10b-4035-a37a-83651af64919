﻿I'm sorry, but an uncaught exception occurred.

While running game code:
Exception: Line with id intro_b186795b appeared twice, at game/scenes/1.intro.rpy:18 and game/scenes/1.intro.rpy:18.

-- Full Traceback ------------------------------------------------------------

Full traceback:
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\bootstrap.py", line 277, in bootstrap
    renpy.main.main()
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\main.py", line 490, in main
    renpy.game.script.load_script() # sets renpy.game.script.
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\script.py", line 297, in load_script
    self.load_appropriate_file(".rpyc", ".rpy", dir, fn, initcode)
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\script.py", line 809, in load_appropriate_file
    self.finish_load(stmts, initcode, filename=lastfn) # type: ignore
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\script.py", line 427, in finish_load
    self.translator.take_translates(all_stmts)
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\translation\__init__.py", line 166, in take_translates
    raise Exception("Line with id %s appeared twice, at %s:%d and %s:%d." %
Exception: Line with id intro_b186795b appeared twice, at game/scenes/1.intro.rpy:18 and game/scenes/1.intro.rpy:18.

Windows-10-10.0.19043 AMD64
Ren'Py 8.0.3.22090809
A Couples Duet of Love & Lust 0.14.5
Fri May 30 22:26:31 2025
