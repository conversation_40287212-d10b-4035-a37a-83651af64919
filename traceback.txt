﻿I'm sorry, but an uncaught exception occurred.

While running game code:
  File "game/scenes/31.matt_text.rpy", line 191, in script
    "Even before opening the image, <PERSON> had a good idea what kind of image <PERSON> might have sent."
Exception: Open text tag at end of string '{收到图像。'.

-- Full Traceback ------------------------------------------------------------

Full traceback:
  File "game/scenes/31.matt_text.rpy", line 191, in script
    "Even before opening the image, <PERSON> had a good idea what kind of image <PERSON> might have sent."
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\ast.py", line 921, in execute
    renpy.exports.say(who, what, *args, **kwargs)
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\exports.py", line 1373, in say
    who(what, *args, **kwargs)
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\character.py", line 1266, in __call__
    self.do_display(who, what, cb_args=self.cb_args, dtt=dtt, **display_args)
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\character.py", line 927, in do_display
    display_say(who,
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\character.py", line 666, in display_say
    rv = renpy.ui.interact(mouse='say', type=type, roll_forward=roll_forward)
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\ui.py", line 299, in interact
    rv = renpy.game.interface.interact(roll_forward=roll_forward, **kwargs)
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\display\core.py", line 3377, in interact
    repeat, rv = self.interact_core(preloads=preloads, trans_pause=trans_pause, pause=pause, pause_start=pause_start, pause_modal=pause_modal, **kwargs) # type: ignore
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\display\core.py", line 3810, in interact_core
    root_widget.visit_all(lambda i : i.per_interact())
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\display\core.py", line 582, in visit_all
    d.visit_all(callback, seen)
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\display\core.py", line 582, in visit_all
    d.visit_all(callback, seen)
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\display\core.py", line 582, in visit_all
    d.visit_all(callback, seen)
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\display\screen.py", line 456, in visit_all
    self.child.visit_all(callback, seen=None)
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\display\core.py", line 582, in visit_all
    d.visit_all(callback, seen)
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\display\core.py", line 582, in visit_all
    d.visit_all(callback, seen)
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\display\core.py", line 582, in visit_all
    d.visit_all(callback, seen)
  [Previous line repeated 3 more times]
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\display\core.py", line 572, in visit_all
    for d in self.visit():
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\text\text.py", line 1835, in visit
    self.update()
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\text\text.py", line 1812, in update
    tokens = self.tokenize(text)
  File "Z:\Users\ljxmcm\Downloads\A_Couples_Duet-0.14.5-pc\renpy\text\text.py", line 2280, in tokenize
    tokens.extend(textsupport.tokenize(i))
  File "textsupport.pyx", line 154, in renpy.text.textsupport.tokenize
Exception: Open text tag at end of string '{收到图像。'.

Windows-10-10.0.19043 AMD64
Ren'Py 8.0.3.22090809
A Couples Duet of Love & Lust 0.14.5
Thu Apr 24 21:42:21 2025
