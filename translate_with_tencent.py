#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import json
import random
import hashlib
import hmac
import requests
import argparse
from datetime import datetime, timezone

class TencentTranslator:
    """腾讯云翻译API封装类"""

    def __init__(self, secret_id, secret_key, region="ap-guangzhou"):
        """
        初始化翻译器

        Args:
            secret_id: 腾讯云API密钥ID
            secret_key: 腾讯云API密钥Key
            region: 地域信息，默认为广州
        """
        self.secret_id = secret_id
        self.secret_key = secret_key
        self.region = region
        self.endpoint = "tmt.tencentcloudapi.com"
        self.service = "tmt"
        self.version = "2018-03-21"
        self.action = "TextTranslate"

        # 请求频率控制
        self.request_interval = 0.2  # 默认每次请求间隔0.2秒
        self.last_request_time = 0

        # 统计信息
        self.total_chars_translated = 0
        self.total_requests = 0

    def _get_sign(self, params):
        """
        生成腾讯云API请求签名

        Args:
            params: 请求参数字典

        Returns:
            签名字符串
        """
        # 1. 拼接规范请求串
        canonical_uri = "/"
        canonical_querystring = ""

        # 参数排序
        sorted_params = sorted(params.items(), key=lambda x: x[0])
        canonical_headers = "content-type:application/json\nhost:" + self.endpoint + "\n"
        signed_headers = "content-type;host"

        # 将参数拼接为查询字符串
        canonical_querystring = "&".join([f"{k}={v}" for k, v in sorted_params])

        # 组合成请求字符串
        payload = "POST\n" + canonical_uri + "\n" + canonical_querystring + "\n" + canonical_headers + "\n" + signed_headers + "\n" + hashlib.sha256("".encode("utf-8")).hexdigest()

        # 2. 拼接待签名字符串
        algorithm = "TC3-HMAC-SHA256"
        timestamp = int(params["Timestamp"])
        date = datetime.fromtimestamp(timestamp, tz=timezone.utc).strftime("%Y-%m-%d")
        credential_scope = date + "/" + self.service + "/tc3_request"
        hashed_canonical_request = hashlib.sha256(payload.encode("utf-8")).hexdigest()
        string_to_sign = algorithm + "\n" + str(timestamp) + "\n" + credential_scope + "\n" + hashed_canonical_request

        # 3. 计算签名
        # 计算派生签名密钥
        secret_date = self._hmac_sha256(("TC3" + self.secret_key).encode("utf-8"), date)
        secret_service = self._hmac_sha256(secret_date, self.service)
        secret_signing = self._hmac_sha256(secret_service, "tc3_request")
        signature = hmac.new(secret_signing, string_to_sign.encode("utf-8"), hashlib.sha256).hexdigest()

        # 4. 拼接Authorization
        authorization = (algorithm + " " +
                        "Credential=" + self.secret_id + "/" + credential_scope + ", " +
                        "SignedHeaders=" + signed_headers + ", " +
                        "Signature=" + signature)

        return authorization

    def _hmac_sha256(self, key, msg):
        """
        HMAC-SHA256算法

        Args:
            key: 密钥
            msg: 消息

        Returns:
            HMAC值
        """
        return hmac.new(key, msg.encode("utf-8"), hashlib.sha256).digest()

    def translate(self, text, source="en", target="zh", project_id=0):
        """
        翻译文本

        Args:
            text: 待翻译文本
            source: 源语言，默认为英语
            target: 目标语言，默认为中文
            project_id: 项目ID，默认为0

        Returns:
            翻译后的文本
        """
        # 控制请求频率
        current_time = time.time()
        if current_time - self.last_request_time < self.request_interval:
            time.sleep(self.request_interval - (current_time - self.last_request_time))

        # 准备请求参数
        params = {
            "Action": self.action,
            "Version": self.version,
            "Region": self.region,
            "SourceText": text,
            "Source": source,
            "Target": target,
            "ProjectId": project_id,
            "Timestamp": int(time.time()),
            "Nonce": random.randint(1, 10000),
            "SecretId": self.secret_id
        }

        # 构建请求
        url = f"https://{self.endpoint}"
        headers = {
            "Content-Type": "application/json",
            "Host": self.endpoint,
            "X-TC-Action": self.action,
            "X-TC-Version": self.version,
            "X-TC-Region": self.region,
            "X-TC-Timestamp": str(params["Timestamp"]),
            "Authorization": self._get_sign(params)
        }

        # 发送请求
        try:
            response = requests.post(url, headers=headers, data=json.dumps(params))
            self.last_request_time = time.time()
            self.total_requests += 1
            self.total_chars_translated += len(text)

            # 解析响应
            result = response.json()
            if "Response" in result and "TargetText" in result["Response"]:
                return result["Response"]["TargetText"]
            else:
                error_msg = result.get("Response", {}).get("Error", {}).get("Message", "Unknown error")
                print(f"翻译失败: {error_msg}")
                return None
        except Exception as e:
            print(f"请求异常: {str(e)}")
            return None

def translate_file(translator, input_file, output_file, source="en", target="zh"):
    """
    翻译文件并按Ren'Py翻译格式输出

    Args:
        translator: 翻译器实例
        input_file: 输入文件路径
        output_file: 输出文件路径
        source: 源语言
        target: 目标语言

    Returns:
        成功翻译的行数
    """
    try:
        # 读取文件内容
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # 过滤空行，但保留空行的位置信息
        filtered_lines = []
        line_positions = []  # 记录非空行在原文件中的位置

        for i, line in enumerate(lines):
            line = line.strip()
            if line:  # 只处理非空行
                filtered_lines.append(line)
                line_positions.append(i)

        print(f"开始翻译文件: {input_file}")
        print(f"总行数: {len(filtered_lines)} (非空行)")

        # 准备翻译结果
        translated_lines = []
        success_count = 0

        # 逐行翻译
        for i, line in enumerate(filtered_lines):
            # 翻译非空行
            translated = translator.translate(line, source, target)
            if translated:
                translated_lines.append((line, translated))
                success_count += 1
            else:
                # 翻译失败，保留原文
                translated_lines.append((line, line))

            # 显示进度
            if (i + 1) % 10 == 0 or i == len(filtered_lines) - 1:
                print(f"进度: {i+1}/{len(filtered_lines)} ({(i+1)/len(filtered_lines)*100:.1f}%)")

        # 保存翻译结果为Ren'Py格式
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            # 写入Ren'Py翻译头
            f.write("translate chinese strings:\n")

            # 写入每个翻译条目
            for i, (original, translated) in enumerate(translated_lines):
                f.write(f'    old "{original}"\n')
                f.write(f'    new "{translated}"\n')

                # 每个条目之间空一行，但最后一个条目后不空行
                if i < len(translated_lines) - 1:
                    f.write("\n")

        print(f"翻译完成: {success_count}/{len(filtered_lines)} 行已翻译")
        return success_count

    except Exception as e:
        print(f"翻译文件 {input_file} 时出错: {str(e)}")
        return 0

def batch_translate(translator, input_folder, output_folder, source="en", target="zh", limit=None):
    """
    批量翻译文件夹中的所有文件

    Args:
        translator: 翻译器实例
        input_folder: 输入文件夹路径
        output_folder: 输出文件夹路径
        source: 源语言
        target: 目标语言
        limit: 限制处理的文件数量，None表示处理所有文件
    """
    # 创建输出文件夹
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"创建输出文件夹: {output_folder}")

    # 获取所有文本文件
    txt_files = []
    for root, _, files in os.walk(input_folder):
        for file in files:
            if file.endswith(".txt"):
                txt_files.append(os.path.join(root, file))

    if not txt_files:
        print(f"在 {input_folder} 中没有找到.txt文件")
        return

    # 按文件名排序
    txt_files.sort()

    # 如果有限制，只处理指定数量的文件
    if limit is not None and limit > 0:
        txt_files = txt_files[:limit]
        print(f"将处理前 {limit} 个文件")

    print(f"找到 {len(txt_files)} 个要处理的文件")

    # 统计信息
    total_files = len(txt_files)
    processed_files = 0
    total_lines_translated = 0

    # 处理每个文件
    for file_path in txt_files:
        # 获取文件名（不含路径和后缀）
        file_name = os.path.basename(file_path)
        file_name_without_ext = os.path.splitext(file_name)[0]

        # 设置输出文件路径，改为.rpy后缀
        output_file = os.path.join(output_folder, f"{file_name_without_ext}.rpy")

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # 翻译文件
        lines_translated = translate_file(translator, file_path, output_file, source, target)
        total_lines_translated += lines_translated

        processed_files += 1
        print(f"进度: {processed_files}/{total_files} 文件 ({processed_files/total_files*100:.1f}%)")
        print("-" * 50)

    # 打印统计信息
    print("\n翻译完成!")
    print(f"总文件数: {processed_files}")
    print(f"总翻译行数: {total_lines_translated}")
    print(f"总字符数: {translator.total_chars_translated}")
    print(f"总API请求次数: {translator.total_requests}")

def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='使用腾讯云翻译API批量翻译文本文件')
    parser.add_argument('--secret_id', required=True, help='腾讯云API密钥ID')
    parser.add_argument('--secret_key', required=True, help='腾讯云API密钥Key')
    parser.add_argument('--input_folder', default='extracted_quotes', help='输入文件夹路径')
    parser.add_argument('--output_folder', default='/Users/<USER>/Downloads/A_Couples_Duet-0.14.5-pc/game/tl/chinese/scenes', help='输出文件夹路径')
    parser.add_argument('--source', default='en', help='源语言，默认为英语')
    parser.add_argument('--target', default='zh', help='目标语言，默认为中文')
    parser.add_argument('--limit', type=int, default=5, help='限制处理的文件数量，默认为5')

    # 解析命令行参数
    args = parser.parse_args()

    # 创建翻译器实例
    translator = TencentTranslator(args.secret_id, args.secret_key)

    # 批量翻译
    batch_translate(translator, args.input_folder, args.output_folder, args.source, args.target, args.limit)

if __name__ == "__main__":
    main()
