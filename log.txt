Fri May 30 22:27:16 2025
Windows-10-10.0.19043
Ren'Py 8.0.3.22090809
 

Bootstrap to the start of init.init took 0.05s
Early init took 0.01s
Loader init took 3.04s
Loading error handling took 0.09s
Loading script took 8.60s
Loading save slot metadata. took 0.72s
Loading persistent took 0.02s
Faled to initialize steam: FileNotFoundError("Could not find module 'Z:\\Users\\ljxmcm\\Downloads\\A_Couples_Duet-0.14.5-pc\\lib\\py3-windows-x86_64\\steam_api64.dll' (or one of its dependencies). Try using the full path with constructor syntax.")
Set script version to: (8, 0, 3)
Running init code took 0.21s
Loading analysis data took 0.27s
Analyze and compile ATL took 0.54s
Index archives took 0.00s
Dump and make backups. took 0.00s
Cleaning cache took 0.00s
Making clean stores took 0.00s
Initial gc. took 0.53s
DPI scale factor: 1.000000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: b"Couldn't load nvlib." (can be ignored)
Creating interface object took 0.01s
Cleaning stores took 0.00s
Init translation took 0.51s
Build styles took 0.00s
Load screen analysis took 0.00s
Analyze screens took 0.01s
Save screen analysis took 0.02s
Prepare screens took 0.05s
Save pyanalysis. took 0.00s
Save bytecode. took 0.35s
Running _start took 0.01s
Performance test:
Interface start took 0.86s

Initializing gl2 renderer:
primary display bounds: (0, 0, 1512, 982)
swap interval: 1 frames
Windowed mode.
UGUU couldn't find b'glBindVertexArray': b'No OpenGL context has been made current'
UGUU couldn't find b'glClearBufferfi': b'No OpenGL context has been made current'
UGUU couldn't find b'glClearBufferfv': b'No OpenGL context has been made current'
UGUU couldn't find b'glClearBufferiv': b'No OpenGL context has been made current'
UGUU couldn't find b'glClearBufferuiv': b'No OpenGL context has been made current'
UGUU couldn't find b'glGetVertexAttribIiv': b'No OpenGL context has been made current'
UGUU couldn't find b'glGetVertexAttribIuiv': b'No OpenGL context has been made current'
UGUU couldn't find b'glMapBufferRange': b'No OpenGL context has been made current'
UGUU couldn't find b'glVertexAttribI4i': b'No OpenGL context has been made current'
UGUU couldn't find b'glVertexAttribI4iv': b'No OpenGL context has been made current'
UGUU couldn't find b'glVertexAttribI4ui': b'No OpenGL context has been made current'
UGUU couldn't find b'glVertexAttribI4uiv': b'No OpenGL context has been made current'
UGUU couldn't find b'glVertexAttribIPointer': b'No OpenGL context has been made current'
Vendor: "b'Apple'"
Renderer: b'Apple M1 Pro'
Version: b'2.1 Metal - 88.1'
Display Info: None
Screen sizes: virtual=(1920, 1080) physical=(1410, 793) drawable=(1410, 793)
Maximum texture size: 4096x4096
Screen sizes: virtual=(1920, 1080) physical=(1410, 789) drawable=(1410, 789)
Maximum texture size: 4096x4096
