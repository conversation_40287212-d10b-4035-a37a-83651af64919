#!/bin/bash

# 创建工作目录
mkdir -p Wine解决方案/工作目录
mkdir -p Wine解决方案/工作目录/原始文件
mkdir -p Wine解决方案/工作目录/切片文件
mkdir -p Wine解决方案/工作目录/翻译文件
mkdir -p Wine解决方案/工作目录/合并文件

# 使脚本可执行
chmod +x Wine解决方案/切片文件.py
chmod +x Wine解决方案/合并文件.py
chmod +x Wine解决方案/生成翻译文件.py

# 函数：处理单个文件
process_file() {
    local input_file=$1
    local base_name=$(basename "$input_file" .rpy)
    
    echo "处理文件: $base_name"
    
    # 复制原始文件
    cp "$input_file" "Wine解决方案/工作目录/原始文件/${base_name}.rpy"
    
    # 创建切片目录
    mkdir -p "Wine解决方案/工作目录/切片文件/${base_name}"
    
    # 切片文件
    python3 Wine解决方案/切片文件.py "Wine解决方案/工作目录/原始文件/${base_name}.rpy" "Wine解决方案/工作目录/切片文件/${base_name}" --lines 50
    
    echo "文件 $base_name 处理完成"
}

# 主函数
main() {
    # 检查参数
    if [ $# -eq 0 ]; then
        echo "用法: $0 <文件路径1> [文件路径2] ..."
        exit 1
    fi
    
    # 处理每个文件
    for file in "$@"; do
        if [ -f "$file" ]; then
            process_file "$file"
        else
            echo "文件不存在: $file"
        fi
    done
    
    echo "所有文件处理完成"
}

# 执行主函数
main "$@"
