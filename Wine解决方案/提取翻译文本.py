#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import re
import argparse

def extract_translatable_strings(input_file, output_file):
    """
    从Ren'Py脚本文件中提取需要翻译的字符串，并生成翻译文件
    """
    # 读取输入文件
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取对话和旁白
    # 这个正则表达式可能需要根据实际文件格式进行调整
    pattern = r'(?:^|\n)\s*(?:([a-zA-Z0-9_]+)\s+)?\"(.*?)\"'
    matches = re.findall(pattern, content, re.DOTALL)
    
    # 生成翻译文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# TODO: Translation updated at 2023-04-22 10:00\n\n")
        f.write("translate chinese strings:\n\n")
        
        for speaker, text in matches:
            if text.strip():  # 忽略空字符串
                if speaker:
                    f.write(f'    # {speaker} "{text}"\n')
                else:
                    f.write(f'    # "{text}"\n')
                
                f.write(f'    old "{text}"\n')
                f.write(f'    new "{text}"\n\n')
    
    print(f"已从 {input_file} 提取翻译文本并保存到 {output_file}")

def main():
    parser = argparse.ArgumentParser(description='从Ren\'Py脚本文件中提取需要翻译的字符串')
    parser.add_argument('input_file', help='输入的Ren\'Py脚本文件')
    parser.add_argument('output_file', help='输出的翻译文件')
    
    args = parser.parse_args()
    
    extract_translatable_strings(args.input_file, args.output_file)

if __name__ == '__main__':
    main()
