#!/bin/bash

# 创建工作目录
mkdir -p Wine解决方案/工作目录
mkdir -p Wine解决方案/工作目录/原始文件
mkdir -p Wine解决方案/工作目录/切片文件
mkdir -p Wine解决方案/工作目录/翻译文件

# 使脚本可执行
chmod +x Wine解决方案/切片文件.py
chmod +x Wine解决方案/提取翻译文本.py

# 函数：处理单个文件
process_file() {
    local input_file=$1
    local base_name=$(basename "$input_file" .rpyc)
    
    echo "处理文件: $base_name"
    
    # 创建切片目录
    mkdir -p "Wine解决方案/工作目录/切片文件/${base_name}"
    mkdir -p "Wine解决方案/工作目录/翻译文件/${base_name}"
    
    # 反编译rpyc文件
    echo "反编译文件..."
    python3 -c "import pickle, sys; data=pickle.load(open('$input_file', 'rb')); print(data)" > "Wine解决方案/工作目录/原始文件/${base_name}.rpy" 2>/dev/null || echo "反编译失败，可能需要使用专门的工具"
    
    # 如果反编译失败，尝试使用其他方法
    if [ ! -s "Wine解决方案/工作目录/原始文件/${base_name}.rpy" ]; then
        echo "尝试使用其他方法提取文本..."
        strings "$input_file" | grep -E '^[a-zA-Z0-9_]+ ".+"$|^".+"$' > "Wine解决方案/工作目录/原始文件/${base_name}.rpy"
    fi
    
    # 切片文件
    echo "切片文件..."
    python3 Wine解决方案/切片文件.py "Wine解决方案/工作目录/原始文件/${base_name}.rpy" "Wine解决方案/工作目录/切片文件/${base_name}" --lines 50
    
    # 为每个切片生成翻译文件
    echo "生成翻译文件..."
    for slice_file in Wine解决方案/工作目录/切片文件/${base_name}/*.rpy; do
        slice_base_name=$(basename "$slice_file" .rpy)
        python3 Wine解决方案/提取翻译文本.py "$slice_file" "Wine解决方案/工作目录/翻译文件/${base_name}/${slice_base_name}_trans.rpy"
    done
    
    echo "文件 $base_name 处理完成"
}

# 主函数
main() {
    # 检查参数
    if [ $# -eq 0 ]; then
        echo "用法: $0 <文件路径1> [文件路径2] ..."
        exit 1
    fi
    
    # 处理每个文件
    for file in "$@"; do
        if [ -f "$file" ]; then
            process_file "$file"
        else
            echo "文件不存在: $file"
        fi
    done
    
    echo "所有文件处理完成"
}

# 执行主函数
main "$@"
