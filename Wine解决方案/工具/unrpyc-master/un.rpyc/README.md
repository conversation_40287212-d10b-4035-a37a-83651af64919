This folder contains the scripts necessary to compile the decompiler into un.rpyc, a file which will
automatically decompile a ren'py game once it's loaded by ren'py. To do this, simply run compile.py
using python 2.7.

.rpyc files are gzip compressed pickle files. Using the pickleast module we can exploit this by
creating a pickle which will run the decompiler when ren'py is loading all files, before any .rpy
code normally even runs.

Note that for this to work the module
`pickleast` (from https://github.com/CensoredUsername/picklemagic)
must be in pythons module search path. If it isn't, you can use the -m (--magic) switch to specify the
directory in which pickleast.py is located.

Via various ways of optimization this module manages to fit the whole decompiler in a file less
than 16 kB in size! This is smaller than some of the unrpyc source files.

The -d (--debug) switch turns on debug results. In this case, three extra files will be produced.

un.dis contains pickle disassembly of the pickle itself.
un.dis2 contains pickle disassembly of the gzipped string inside of un.dis
un.dis3 contains a representation of the AST which is pickled to produce un.dis2

The -p (--protocol) switch controls which pickle protocol is used (very old ren'py games might not be able to
unpickle files generated by protocol 2. Besides, protocol 1 seems to achieve superior compression
results.)

The -r (--raw) switch turns off minimization of the included unrpyc modules.

Finally the -o (--obfuscate) switch enables extra minification features which
don't change the filesize too much but obfuscate the included modules more.

For the best result, use

[python2] compile.py -d -o -p 1