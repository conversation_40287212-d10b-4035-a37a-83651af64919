# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Build and test status

on:
  push:
    branches: [ master, dev ]
  pull_request:
    branches: [ master, dev ]

jobs:
  tests-py3:
    name: Runs tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.12"]

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    - name: Test by decompiling a script and building un.rpyc
      run: |
        # test the command line tool
        ./unrpyc.py --clobber "testcases/compiled/**/*.rpyc"
        diff -ur testcases/expected testcases/compiled -x "*.rpyc"
        # compile un.rpyc/rpy/rpyb
        cd un.rpyc;
        ./compile.py -p 1
        cd ..
        # test all the different iterations of it
        ./testcases/test_un_rpyc.py --unrpyc un.rpyc/un.rpyc "testcases/compiled/**/*.rpyc"
        diff -ur testcases/expected testcases/compiled -x "*.rpyc"
        ./testcases/test_un_rpyc.py --unrpyb un.rpyc/bytecode-39.rpyb "testcases/compiled/**/*.rpyc"
        diff -ur testcases/expected testcases/compiled -x "*.rpyc"
        ./testcases/test_un_rpyc.py --unrpy un.rpyc/un.rpy "testcases/compiled/**/*.rpyc"
        diff -ur testcases/expected testcases/compiled -x "*.rpyc"
