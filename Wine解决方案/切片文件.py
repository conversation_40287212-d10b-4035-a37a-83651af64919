#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse

def slice_file(input_file, output_dir, lines_per_slice=50):
    """
    将输入文件切片成多个小文件，每个文件包含指定行数
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取输入文件
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 计算需要多少个切片
    total_lines = len(lines)
    num_slices = (total_lines + lines_per_slice - 1) // lines_per_slice
    
    # 生成文件名的基础部分
    base_name = os.path.basename(input_file)
    base_name = os.path.splitext(base_name)[0]
    
    # 切片并保存
    for i in range(num_slices):
        start_line = i * lines_per_slice
        end_line = min((i + 1) * lines_per_slice, total_lines)
        
        slice_content = lines[start_line:end_line]
        
        # 生成输出文件名
        output_file = os.path.join(output_dir, f"{base_name}_slice_{i+1:03d}.rpy")
        
        # 保存切片
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(slice_content)
    
    print(f"文件已切片为 {num_slices} 个部分，保存在 {output_dir} 目录中")

def main():
    parser = argparse.ArgumentParser(description='将大文件切片成小文件')
    parser.add_argument('input_file', help='输入文件路径')
    parser.add_argument('output_dir', help='输出目录路径')
    parser.add_argument('--lines', type=int, default=50, help='每个切片的行数 (默认: 50)')
    
    args = parser.parse_args()
    
    slice_file(args.input_file, args.output_dir, args.lines)

if __name__ == '__main__':
    main()
