#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import re
import argparse

def extract_strings(input_file):
    """
    从Ren'Py脚本文件中提取需要翻译的字符串
    """
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取对话行
    # 这个正则表达式可能需要根据实际文件格式进行调整
    dialog_pattern = r'^\s*([a-zA-Z0-9_]+)\s+\"(.+?)\"'
    dialogs = re.findall(dialog_pattern, content, re.MULTILINE)
    
    # 提取旁白行
    narration_pattern = r'^\s*\"(.+?)\"'
    narrations = re.findall(narration_pattern, content, re.MULTILINE)
    
    return dialogs, narrations

def generate_translation_file(input_file, output_file):
    """
    生成Ren'Py翻译文件
    """
    dialogs, narrations = extract_strings(input_file)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# TODO: Translation updated at " + 
                "2023-04-22 10:00\n\n")
        
        f.write("translate chinese strings:\n\n")
        
        # 处理对话
        for character, text in dialogs:
            f.write(f'    # {character} "{text}"\n')
            f.write(f'    old "{text}"\n')
            f.write(f'    new "{text}"\n\n')
        
        # 处理旁白
        for text in narrations:
            f.write(f'    # "{text}"\n')
            f.write(f'    old "{text}"\n')
            f.write(f'    new "{text}"\n\n')

def main():
    parser = argparse.ArgumentParser(description='生成Ren\'Py翻译文件')
    parser.add_argument('input_file', help='输入的Ren\'Py脚本文件')
    parser.add_argument('output_file', help='输出的翻译文件')
    
    args = parser.parse_args()
    
    generate_translation_file(args.input_file, args.output_file)
    print(f"翻译文件已生成: {args.output_file}")

if __name__ == '__main__':
    main()
