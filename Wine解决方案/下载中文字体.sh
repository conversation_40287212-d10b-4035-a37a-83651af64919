#!/bin/bash

# 创建临时目录
mkdir -p temp_fonts

# 下载思源黑体
echo "正在下载思源黑体..."
curl -L "https://github.com/adobe-fonts/source-han-sans/releases/download/2.004R/SourceHanSansSC.zip" -o temp_fonts/SourceHanSansSC.zip

# 解压字体
echo "正在解压字体..."
unzip -q temp_fonts/SourceHanSansSC.zip -d temp_fonts

# 复制字体到游戏目录
echo "正在复制字体到游戏目录..."
mkdir -p game/fonts
cp temp_fonts/OTF/SimplifiedChinese/SourceHanSansSC-Regular.otf game/fonts/
cp temp_fonts/OTF/SimplifiedChinese/SourceHanSansSC-Bold.otf game/fonts/

# 创建字体配置文件
echo "正在创建字体配置文件..."
cat > game/fonts.rpy << EOL
init python:
    # 注册中文字体
    config.font_replacement_map["DejaVuSans.ttf", True, False] = ("SourceHanSansSC-Bold.otf", False, False)
    config.font_replacement_map["DejaVuSans.ttf", False, False] = ("SourceHanSansSC-Regular.otf", False, False)
EOL

# 清理临时文件
echo "正在清理临时文件..."
rm -rf temp_fonts

echo "字体安装完成！"
