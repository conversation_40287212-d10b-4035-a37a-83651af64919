#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import sys

def extract_text_from_file(file_path):
    """
    从文件中提取可能的对话文本
    """
    with open(file_path, 'rb') as f:
        content = f.read()
    
    # 尝试提取字符串
    text_pattern = rb'[\x20-\x7E]{10,}'  # 至少10个可打印ASCII字符
    matches = re.findall(text_pattern, content)
    
    # 过滤可能的对话
    dialogs = []
    for match in matches:
        try:
            text = match.decode('utf-8')
            # 只保留可能是对话的文本
            if re.search(r'^[A-Za-z]', text) and len(text) > 15 and '.' in text:
                dialogs.append(text)
        except:
            pass
    
    return dialogs

def create_translation_file(dialogs, output_file):
    """
    创建翻译文件
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("translate chinese strings:\n")
        f.write("    # 场景38：Nat和Darrel约会后\n")
        
        for dialog in dialogs:
            f.write(f'    old "{dialog}"\n')
            f.write(f'    new "{dialog}"\n\n')

def main():
    if len(sys.argv) != 3:
        print(f"用法: {sys.argv[0]} <输入文件> <输出文件>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    dialogs = extract_text_from_file(input_file)
    create_translation_file(dialogs, output_file)
    
    print(f"已从 {input_file} 提取 {len(dialogs)} 条对话，并保存到 {output_file}")

if __name__ == "__main__":
    main()
