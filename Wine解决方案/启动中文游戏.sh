#!/bin/bash

# 创建输出目录
mkdir -p "Wine解决方案/输出结果"

# 设置环境变量，指定游戏语言为中文
export RENPY_LANGUAGE="chinese"

# 确保字体文件存在
if [ ! -f "game/fonts/Songti.ttc" ]; then
  echo "中文字体文件不存在，正在复制..."
  cp "Wine解决方案/Songti.ttc" "game/fonts/"
fi

echo "尝试使用Wine运行游戏（中文界面）..."
wine A_Couples_Duet.exe > "Wine解决方案/输出结果/wine运行结果.log" 2>&1

# 检查Wine是否成功运行
if [ $? -eq 0 ]; then
  echo "游戏成功启动！"
else
  echo "Wine运行失败，错误信息已保存到Wine解决方案/输出结果/wine运行结果.log"

  # 尝试使用.sh脚本
  echo "尝试使用.sh脚本运行游戏..."
  chmod +x A_Couples_Duet.sh
  RENPY_LANGUAGE="chinese" ./A_Couples_Duet.sh > "Wine解决方案/输出结果/sh运行结果.log" 2>&1

  if [ $? -eq 0 ]; then
    echo "使用.sh脚本成功启动游戏！"
  else
    echo "使用.sh脚本失败，错误信息已保存到Wine解决方案/输出结果/sh运行结果.log"
  fi
fi
