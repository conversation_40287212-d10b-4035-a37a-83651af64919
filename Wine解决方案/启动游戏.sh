#!/bin/bash

# 创建一个目录来存放输出结果
mkdir -p "Wine解决方案/输出结果"

# 设置环境变量，尝试跳过中文翻译
export RENPY_SKIP_TRANSLATIONS=1

# 使用Wine运行游戏，并将输出重定向到文件
wine A_Couples_Duet.exe > "Wine解决方案/输出结果/游戏输出.log" 2>&1

# 如果游戏启动失败，尝试使用.sh脚本
if [ $? -ne 0 ]; then
  echo "使用Wine运行exe文件失败，尝试使用.sh脚本..."
  chmod +x A_Couples_Duet.sh
  ./A_Couples_Duet.sh > "Wine解决方案/输出结果/脚本输出.log" 2>&1
fi

# 如果还是失败，尝试使用Python脚本
if [ $? -ne 0 ]; then
  echo "使用.sh脚本失败，尝试使用Python脚本..."
  python3 A_Couples_Duet.py > "Wine解决方案/输出结果/python输出.log" 2>&1
fi

echo "所有尝试完成，请查看输出日志文件了解详情。"
