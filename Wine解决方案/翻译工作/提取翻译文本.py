#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import re

def extract_translatable_strings(input_file, output_file, scene_name):
    """
    从Ren'Py脚本文件中提取需要翻译的字符串，并生成翻译文件
    """
    # 读取输入文件
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取对话和旁白
    # 对话格式：e "This is a dialogue."
    dialogue_pattern = r'([a-zA-Z0-9_]+)\s+"([^"]+)"'
    dialogues = re.findall(dialogue_pattern, content)
    
    # 旁白格式："This is a narration."
    narration_pattern = r'^\s*"([^"]+)"'
    narrations = re.findall(narration_pattern, content, re.MULTILINE)
    
    # 生成翻译文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("translate chinese strings:\n")
        f.write(f"    # {scene_name}\n")
        
        # 处理对话
        for speaker, text in dialogues:
            if text.strip():  # 忽略空字符串
                f.write(f'    old "{text}"\n')
                f.write(f'    new "{text}"\n\n')
        
        # 处理旁白
        for text in narrations:
            if text.strip():  # 忽略空字符串
                f.write(f'    old "{text}"\n')
                f.write(f'    new "{text}"\n\n')

def main():
    if len(sys.argv) != 4:
        print(f"用法: {sys.argv[0]} <输入文件> <输出文件> <场景名称>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    scene_name = sys.argv[3]
    
    extract_translatable_strings(input_file, output_file, scene_name)
    print(f"已从 {input_file} 提取翻译文本并保存到 {output_file}")

if __name__ == "__main__":
    main()
