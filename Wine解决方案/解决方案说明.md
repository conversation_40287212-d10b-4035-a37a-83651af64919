# 使用Wine运行带中文翻译的Ren'Py游戏解决方案

## 问题描述

在尝试使用Wine运行A_Couples_Duet.exe时，游戏因中文翻译文件中的重复翻译项而崩溃，错误信息为：

```
Exception: A translation for "Ethan loved his wife." already exists at game/tl/chinese/scenes/10.natdar_gym1_p3.rpy:91.
```

同时，我们希望游戏能够正确显示中文界面。

## 解决方案

我们采用了以下方法解决这个问题：

### 1. 修改Ren'Py翻译系统

我们修改了Ren'Py的翻译系统，使其忽略重复的翻译项而不是抛出异常。具体修改了`renpy/translation/__init__.py`文件中的`StringTranslator.add`方法：

```python
def add(self, old, new, newloc):
    if old in self.translations:
        # 忽略重复的翻译项，不抛出异常
        # 如果已经存在翻译，我们保留第一个翻译
        return

    self.translations[old] = new

    if newloc is not None:
        self.translation_loc[old] = newloc
```

这样，当遇到重复的翻译项时，游戏会保留第一个翻译并继续运行，而不是崩溃。

### 2. 添加中文字体

我们使用宋体（Songti.ttc）作为游戏的中文字体。宋体是一种常用的中文字体，支持简体中文显示。我们将宋体文件复制到游戏的字体目录中。

我们还创建了一个字体配置文件`game/fonts.rpy`，将游戏的默认字体替换为宋体：

```python
init:
    $ config.font_replacement_map[("DejaVuSans.ttf", True, False)] = ("Songti.ttc", False, False)
    $ config.font_replacement_map[("DejaVuSans.ttf", False, False)] = ("Songti.ttc", False, False)
```

### 3. 创建启动脚本

我们创建了一个启动脚本`启动中文游戏.sh`，它会设置环境变量`RENPY_LANGUAGE="chinese"`，确保游戏以中文界面启动。脚本还会检查中文字体是否存在，如果不存在，会自动下载和安装。

## 使用方法

1. 打开终端，进入游戏目录
2. 运行以下命令：
   ```
   ./Wine解决方案/启动中文游戏.sh
   ```
3. 如果游戏成功启动，你将看到中文界面
4. 如果游戏启动失败，可以查看输出日志文件了解详情：
   ```
   cat "Wine解决方案/输出结果/wine运行结果.log"
   ```

## 技术细节

### 修改的文件

1. `renpy/translation/__init__.py` - 修改了翻译系统，使其忽略重复的翻译项
2. `game/tl/chinese/scenes/10.natdar_gym1_p3.rpy` - 注释掉了重复的翻译项（可选）
3. `game/fonts.rpy` - 创建了字体配置文件，将游戏的默认字体替换为宋体
4. `game/fonts/Songti.ttc` - 添加了宋体字体文件

### 环境变量

- `RENPY_LANGUAGE="chinese"` - 设置游戏的语言为中文

### 脚本文件

1. `Wine解决方案/启动中文游戏.sh` - 启动游戏的脚本，设置环境变量并检查字体

## 可能遇到的其他问题

1. **其他重复翻译项**：如果游戏中还有其他重复的翻译项，我们的修改应该能够处理它们，不会导致游戏崩溃。

2. **汉字显示为方块**：如果汉字仍然显示为方块，可能是因为字体文件没有被正确加载。可以尝试以下方法：
   - 确保`game/fonts`目录中存在思源黑体文件
   - 确保`game/fonts.rpy`文件内容正确
   - 尝试使用其他中文字体，如微软雅黑或宋体

3. **Wine兼容性问题**：某些版本的Wine可能与游戏不兼容，可以尝试安装不同版本的Wine：
   ```
   brew install --cask --no-quarantine wine-devel
   ```

4. **缺少依赖**：Wine可能缺少运行游戏所需的某些Windows依赖，可以尝试使用winetricks安装它们：
   ```
   brew install winetricks
   winetricks d3dx9
   ```

5. **Ren'Py平台文件问题**：如果遇到“Ren'Py platform files not found”错误，可能需要下载Ren'Py SDK并将其平台文件复制到游戏目录中。

## 其他可能的解决方法

1. **使用PlayOnMac**：PlayOnMac是macOS上的另一个Wine前端，可能提供更好的兼容性
   ```
   brew install --cask playonmac
   ```

2. **使用原生Ren'Py引擎**：下载Ren'Py引擎并通过它运行游戏
   ```
   brew install --cask renpy
   ```

3. **使用Windows虚拟机**：如果以上方法都不起作用，可以考虑使用虚拟机运行Windows系统，然后在其中运行游戏

## 注意事项

- 我们的修改不会影响游戏的正常功能，只是让它能够处理重复的翻译项
- 如果你想恢复原始的行为，可以将`renpy/translation/__init__.py`文件恢复到原始状态
- 建议备份原始文件，以便在需要时恢复
