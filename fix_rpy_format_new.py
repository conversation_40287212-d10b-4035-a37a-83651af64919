#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import shutil
import sys
from datetime import datetime

def fix_rpy_file(file_path):
    """修复rpy文件的格式问题"""
    # 创建备份
    backup_path = file_path + ".bak"
    shutil.copy2(file_path, backup_path)
    
    with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
        content = f.read()
    
    # 1. 删除sounds/xxx.mp3
    content = re.sub(r'sounds/[^\s"]+\.mp3', "", content)
    
    # 处理文件内容
    lines = content.split('\n')
    fixed_lines = []
    
    for line in lines:
        stripped = line.strip()
        
        # 检查是否是new行
        if stripped.startswith('new '):
            # 2. 只在new行中处理撇号
            if "'" in line or "'" in line:
                # 找到引号之间的内容
                match = re.search(r'new\s+"([^"]*)"', line)
                if match:
                    content_part = match.group(1)  # 引号内的内容
                    
                    # 在内容中的撇号前添加转义符
                    content_part = re.sub(r'(\w)(\'|\')(\w)', r'\1\\\2\3', content_part)
                    
                    # 重建行
                    fixed_line = f'new "{content_part}"'
                    fixed_lines.append(fixed_line)
                    continue
            
            # 3. 修复引号格式问题
            quote_count = stripped.count('"')
            
            # 如果引号数量不是2，尝试修复
            if quote_count != 2:
                # 如果没有引号，添加引号
                if quote_count == 0:
                    content_part = stripped[len('new '):]
                    fixed_line = f'new "{content_part}"'
                    fixed_lines.append(fixed_line)
                    continue
                
                # 如果只有一个引号，检查是开始还是结束引号
                elif quote_count == 1:
                    if '"' in stripped and stripped.index('"') < len(stripped) // 2:
                        # 缺少结束引号
                        fixed_line = stripped + '"'
                        fixed_lines.append(fixed_line)
                        continue
                    elif '"' in stripped:
                        # 缺少开始引号
                        content_start = stripped.index('"')
                        if content_start > len('new '):
                            fixed_line = 'new "' + stripped[content_start+1:]
                            fixed_lines.append(fixed_line)
                            continue
            
            # 如果不需要修复或无法修复，保持原样
            fixed_lines.append(line)
        
        # 检查是否是old行
        elif stripped.startswith('old '):
            # 3. 修复引号格式问题
            quote_count = stripped.count('"')
            
            # 如果引号数量不是2，尝试修复
            if quote_count != 2:
                # 如果没有引号，添加引号
                if quote_count == 0:
                    content_part = stripped[len('old '):]
                    fixed_line = f'old "{content_part}"'
                    fixed_lines.append(fixed_line)
                    continue
                
                # 如果只有一个引号，检查是开始还是结束引号
                elif quote_count == 1:
                    if '"' in stripped and stripped.index('"') < len(stripped) // 2:
                        # 缺少结束引号
                        fixed_line = stripped + '"'
                        fixed_lines.append(fixed_line)
                        continue
                    elif '"' in stripped:
                        # 缺少开始引号
                        content_start = stripped.index('"')
                        if content_start > len('old '):
                            fixed_line = 'old "' + stripped[content_start+1:]
                            fixed_lines.append(fixed_line)
                            continue
            
            # 如果不需要修复或无法修复，保持原样
            fixed_lines.append(line)
        else:
            # 非old/new行，保持原样
            fixed_lines.append(line)
    
    # 将修复后的内容写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(fixed_lines))
    
    return True

def main():
    base_dir = "game/tl/chinese/scenes"
    scene_pattern = re.compile(r'^(3[3-9]|[4-5][0-9]).*\.rpy$')
    
    fixed_files = []
    
    for filename in sorted(os.listdir(base_dir)):
        if scene_pattern.match(filename):
            file_path = os.path.join(base_dir, filename)
            print(f"修复文件: {filename}")
            
            try:
                if fix_rpy_file(file_path):
                    fixed_files.append(filename)
                    print(f"  已修复")
                else:
                    print(f"  无需修复")
            except Exception as e:
                print(f"  修复时出错: {str(e)}")
    
    # 输出修复结果
    if fixed_files:
        print(f"\n已修复 {len(fixed_files)} 个文件:")
        for filename in fixed_files:
            print(f"  - {filename}")
        print("\n所有文件都已备份为.bak文件，如需恢复请使用restore_files.py脚本")
    else:
        print("\n没有文件需要修复！")

if __name__ == "__main__":
    main()
