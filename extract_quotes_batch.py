#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import os
import sys
import argparse

def extract_quotes(file_path):
    """
    从Ren'Py脚本文件中提取所有引号内的文本，并去重
    
    Args:
        file_path: Ren'Py脚本文件的路径
        
    Returns:
        提取的不重复文本列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        # 如果UTF-8解码失败，尝试使用latin-1编码
        with open(file_path, 'r', encoding='latin-1') as f:
            content = f.read()
    
    # 使用正则表达式提取所有引号内的文本
    # 这个正则表达式匹配双引号内的文本，但不匹配转义的双引号
    pattern = r'(?<![\\])\"(.*?)(?<![\\])\"'
    quotes = re.findall(pattern, content)
    
    # 过滤掉空字符串
    quotes = [q for q in quotes if q.strip()]
    
    # 去重
    unique_quotes = []
    seen = set()
    for quote in quotes:
        if quote not in seen:
            seen.add(quote)
            unique_quotes.append(quote)
    
    return unique_quotes

def save_quotes(quotes, output_file):
    """
    将提取的文本保存到文件中，每行文本之间空一行
    
    Args:
        quotes: 提取的文本列表
        output_file: 输出文件的路径
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        for i, quote in enumerate(quotes):
            f.write(f"{quote}\n")
            # 每行之后空一行，但最后一行后不空行
            if i < len(quotes) - 1:
                f.write("\n")

def process_folder(input_folder):
    """
    处理指定文件夹中的所有.rpy文件
    
    Args:
        input_folder: 输入文件夹路径
    """
    # 创建输出文件夹
    output_folder = "extracted_quotes"
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"创建输出文件夹: {output_folder}")
    
    # 获取所有.rpy文件
    rpy_files = []
    for root, dirs, files in os.walk(input_folder):
        for file in files:
            if file.endswith(".rpy"):
                rpy_files.append(os.path.join(root, file))
    
    if not rpy_files:
        print(f"在 {input_folder} 中没有找到.rpy文件")
        return
    
    print(f"找到 {len(rpy_files)} 个.rpy文件")
    
    # 处理每个文件
    for file_path in rpy_files:
        # 获取文件名（不含路径和后缀）
        file_name = os.path.basename(file_path)
        file_name_without_ext = os.path.splitext(file_name)[0]
        
        # 设置输出文件路径
        output_file = os.path.join(output_folder, f"{file_name_without_ext}.txt")
        
        # 提取引号内的文本
        quotes = extract_quotes(file_path)
        
        # 保存提取的文本
        save_quotes(quotes, output_file)
        
        print(f"已从 {file_path} 中提取 {len(quotes)} 条不重复的文本，保存到 {output_file}")

def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='从Ren\'Py脚本文件中提取双引号内的文本')
    parser.add_argument('folder_path', help='包含.rpy文件的文件夹路径')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 处理指定文件夹
    process_folder(args.folder_path)
    
    print("所有文件处理完成！")

if __name__ == "__main__":
    main()
