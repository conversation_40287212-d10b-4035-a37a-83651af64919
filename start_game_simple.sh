#!/bin/bash
# 简化的游戏启动脚本

echo "🎮 启动 A Couple's Duet 游戏 (简化版)"
echo "====================================="

# 设置Wine环境
export WINEPREFIX="$HOME/.wine_renpy"
export WINEARCH=win64

# 设置Wine路径
WINE_PATH="/Applications/Wine Stable.app/Contents/Resources/wine/bin/wine"

# 检查游戏文件
GAME_EXE="../A_Couples_Duet.exe"

echo "✅ Wine前缀: $WINEPREFIX"
echo "✅ 游戏文件: $GAME_EXE"
echo ""

# 启动游戏
echo "🚀 启动游戏..."
cd ..

echo "📋 执行命令: $WINE_PATH \"$GAME_EXE\""
echo ""

# 启动游戏并捕获输出
"$WINE_PATH" "$GAME_EXE" 2>&1

EXIT_CODE=$?
echo ""

if [ $EXIT_CODE -eq 0 ]; then
    echo "🎮 游戏正常退出"
else
    echo "❌ 游戏启动失败 (退出代码: $EXIT_CODE)"
    echo ""
    echo "💡 可能的解决方案:"
    echo "1. 游戏可能需要特定的运行时库"
    echo "2. 尝试在Wine中安装Visual C++ Redistributables"
    echo "3. 检查游戏是否为Ren'Py游戏"
fi
