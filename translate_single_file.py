#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import time
import argparse
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.tmt.v20180321 import tmt_client, models

def translate_text(client, text, source="en", target="zh"):
    """
    使用腾讯云翻译API翻译文本
    
    Args:
        client: 腾讯云翻译客户端
        text: 待翻译文本
        source: 源语言，默认为英语
        target: 目标语言，默认为中文
        
    Returns:
        翻译后的文本，失败返回None
    """
    try:
        # 创建请求对象
        req = models.TextTranslateRequest()
        
        # 设置请求参数
        req.SourceText = text
        req.Source = source
        req.Target = target
        req.ProjectId = 0
        
        # 发送请求
        resp = client.TextTranslate(req)
        
        # 返回翻译结果
        return resp.TargetText
    
    except TencentCloudSDKException as err:
        print(f"翻译失败: {err}")
        return None
    except Exception as e:
        print(f"请求异常: {str(e)}")
        return None

def translate_file(client, input_file, output_file, source="en", target="zh"):
    """
    翻译文件并按Ren'Py翻译格式输出
    
    Args:
        client: 腾讯云翻译客户端
        input_file: 输入文件路径
        output_file: 输出文件路径
        source: 源语言
        target: 目标语言
    
    Returns:
        成功翻译的行数
    """
    try:
        # 读取文件内容
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 过滤空行，但保留空行的位置信息
        filtered_lines = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if line:  # 只处理非空行
                filtered_lines.append(line)
        
        print(f"开始翻译文件: {input_file}")
        print(f"总行数: {len(filtered_lines)} (非空行)")
        
        # 准备翻译结果
        translated_lines = []
        success_count = 0
        
        # 逐行翻译
        for i, line in enumerate(filtered_lines):
            # 翻译非空行
            translated = translate_text(client, line, source, target)
            
            # 添加延迟，避免请求过快
            time.sleep(0.5)
            
            if translated:
                translated_lines.append((line, translated))
                success_count += 1
            else:
                # 翻译失败，保留原文
                translated_lines.append((line, line))
            
            # 显示进度
            if (i + 1) % 10 == 0 or i == len(filtered_lines) - 1:
                print(f"进度: {i+1}/{len(filtered_lines)} ({(i+1)/len(filtered_lines)*100:.1f}%)")
        
        # 保存翻译结果为Ren'Py格式
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            # 写入Ren'Py翻译头
            f.write("translate chinese strings:\n")
            
            # 写入每个翻译条目
            for i, (original, translated) in enumerate(translated_lines):
                f.write(f'    old "{original}"\n')
                f.write(f'    new "{translated}"\n')
                
                # 每个条目之间空一行，但最后一个条目后不空行
                if i < len(translated_lines) - 1:
                    f.write("\n")
        
        print(f"翻译完成: {success_count}/{len(filtered_lines)} 行已翻译")
        return success_count
    
    except Exception as e:
        print(f"翻译文件 {input_file} 时出错: {str(e)}")
        return 0

def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='使用腾讯云翻译API翻译单个文本文件')
    parser.add_argument('--secret_id', required=True, help='腾讯云API密钥ID')
    parser.add_argument('--secret_key', required=True, help='腾讯云API密钥Key')
    parser.add_argument('--region', default='ap-guangzhou', help='腾讯云地域，默认为广州')
    parser.add_argument('--input_file', default='extracted_quotes/1.intro.txt', help='输入文件路径')
    parser.add_argument('--output_file', default='/Users/<USER>/Downloads/A_Couples_Duet-0.14.5-pc/game/tl/chinese/scenes/1.intro.rpy', help='输出文件路径')
    parser.add_argument('--source', default='en', help='源语言，默认为英语')
    parser.add_argument('--target', default='zh', help='目标语言，默认为中文')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    try:
        # 实例化一个认证对象
        cred = credential.Credential(args.secret_id, args.secret_key)
        
        # 实例化一个http选项，可选的，没有特殊需求可以跳过
        httpProfile = HttpProfile()
        httpProfile.endpoint = "tmt.tencentcloudapi.com"
        
        # 实例化一个client选项，可选的，没有特殊需求可以跳过
        clientProfile = ClientProfile()
        clientProfile.httpProfile = httpProfile
        
        # 实例化要请求产品的client对象
        client = tmt_client.TmtClient(cred, args.region, clientProfile)
        
        # 翻译单个文件
        print(f"使用地域: {args.region}")
        print(f"使用SecretId: {args.secret_id}")
        print(f"SecretKey长度: {len(args.secret_key)}")
        
        # 测试连接
        print("测试API连接...")
        test_result = translate_text(client, "Hello world", args.source, args.target)
        if test_result:
            print(f"API连接测试成功! 翻译结果: {test_result}")
            
            # 翻译文件
            translate_file(client, args.input_file, args.output_file, args.source, args.target)
        else:
            print("API连接测试失败，请检查密钥和地域设置")
        
    except TencentCloudSDKException as err:
        print(f"腾讯云SDK异常: {err}")
    except Exception as e:
        print(f"程序异常: {str(e)}")

if __name__ == "__main__":
    main()
