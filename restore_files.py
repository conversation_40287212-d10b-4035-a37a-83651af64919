#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
import sys
import re

def restore_from_backup(base_dir):
    """从备份文件恢复原始文件"""
    scene_pattern = re.compile(r'^(3[3-9]|[4-5][0-9]).*\.rpy\.bak$')
    restored_files = []
    
    for filename in os.listdir(base_dir):
        if scene_pattern.match(filename):
            backup_path = os.path.join(base_dir, filename)
            original_path = backup_path[:-4]  # 移除.bak后缀
            
            try:
                # 检查备份文件是否存在
                if os.path.exists(backup_path):
                    # 恢复原始文件
                    shutil.copy2(backup_path, original_path)
                    # 删除备份文件
                    os.remove(backup_path)
                    restored_files.append(filename[:-4])
                    print(f"已恢复: {filename[:-4]}")
            except Exception as e:
                print(f"恢复 {filename[:-4]} 时出错: {str(e)}")
    
    return restored_files

def main():
    base_dir = "game/tl/chinese/scenes"
    
    print("开始恢复文件...")
    restored_files = restore_from_backup(base_dir)
    
    if restored_files:
        print(f"\n已成功恢复 {len(restored_files)} 个文件:")
        for filename in restored_files:
            print(f"  - {filename}")
    else:
        print("\n没有找到需要恢复的文件！")

if __name__ == "__main__":
    main()
