#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ren'Py游戏汉化工具 - 主程序
使用DeepSeek API进行翻译，严格遵循Ren'Py翻译文件格式规范
"""

import os
import sys
import argparse
import logging
from datetime import datetime
from pathlib import Path

# ===========================================
# DeepSeek API 配置 - 请在下方引号内填入您的API密钥
# ===========================================
DEEPSEEK_API_KEY = "***********************************"  # 在此处粘贴您的DeepSeek API密钥
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions"

# 翻译配置
TARGET_LANGUAGE = "chinese"
BATCH_SIZE = 10  # 批量翻译的文本数量
RETRY_COUNT = 3  # API调用失败重试次数

# 输出路径配置
TEST_OUTPUT_DIR = "output/test/"      # 测试版本输出目录
FINAL_OUTPUT_DIR = "output/final/"    # 最终版本输出目录
# ===========================================

# 导入自定义模块
try:
    from .parser import RenpyParser
    from .translator import DeepSeekTranslator
    from .formatter import RenpyFormatter
    from .utils import setup_logger, TranslationCache
except ImportError:
    # 如果作为脚本直接运行，使用相对导入
    import parser as parser_module
    import translator as translator_module
    import formatter as formatter_module
    import utils as utils_module
    
    RenpyParser = parser_module.RenpyParser
    DeepSeekTranslator = translator_module.DeepSeekTranslator
    RenpyFormatter = formatter_module.RenpyFormatter
    setup_logger = utils_module.setup_logger
    TranslationCache = utils_module.TranslationCache


class RenpyTranslationTool:
    """Ren'Py游戏汉化工具主类"""
    
    def __init__(self, api_key=None, logger=None):
        """
        初始化翻译工具
        
        Args:
            api_key: DeepSeek API密钥
            logger: 日志记录器
        """
        self.api_key = api_key or DEEPSEEK_API_KEY
        self.logger = logger or logging.getLogger(__name__)
        
        # 初始化组件
        self.parser = RenpyParser(logger=self.logger)
        self.translator = DeepSeekTranslator(
            api_key=self.api_key,
            api_url=DEEPSEEK_API_URL,
            logger=self.logger
        )
        self.formatter = RenpyFormatter(logger=self.logger)
        self.cache = TranslationCache()
        
        # 确保输出目录存在
        os.makedirs(TEST_OUTPUT_DIR, exist_ok=True)
        os.makedirs(FINAL_OUTPUT_DIR, exist_ok=True)
    
    def translate_file(self, input_file, output_file=None, test_mode=True):
        """
        翻译单个.rpy文件
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径（可选）
            test_mode: 是否为测试模式
            
        Returns:
            翻译结果统计
        """
        self.logger.info(f"开始翻译文件: {input_file}")
        
        # 解析文件
        try:
            texts = self.parser.parse_file(input_file)
            self.logger.info(f"从文件中提取到 {len(texts)} 个需要翻译的文本")
        except Exception as e:
            self.logger.error(f"解析文件失败: {str(e)}")
            return {"success": False, "error": str(e)}
        
        if not texts:
            self.logger.warning("文件中没有找到需要翻译的文本")
            return {"success": True, "translated_count": 0}
        
        # 翻译文本
        translated_texts = []
        success_count = 0
        
        for i, text_info in enumerate(texts):
            self.logger.info(f"翻译进度: {i+1}/{len(texts)}")
            
            original_text = text_info['text']
            
            # 检查缓存
            cached_translation = self.cache.get(original_text)
            if cached_translation:
                self.logger.debug(f"使用缓存翻译: {original_text[:30]}...")
                translated_texts.append({
                    **text_info,
                    'translated': cached_translation,
                    'from_cache': True
                })
                success_count += 1
                continue
            
            # 调用API翻译
            try:
                result = self.translator.translate(original_text)
                if result['success']:
                    translated_text = result['translated_text']
                    self.cache.set(original_text, translated_text)
                    translated_texts.append({
                        **text_info,
                        'translated': translated_text,
                        'from_cache': False
                    })
                    success_count += 1
                    self.logger.debug(f"翻译成功: {translated_text[:30]}...")
                else:
                    self.logger.warning(f"翻译失败: {result.get('error', '未知错误')}")
                    translated_texts.append({
                        **text_info,
                        'translated': original_text,  # 保持原文
                        'from_cache': False,
                        'error': result.get('error')
                    })
            except Exception as e:
                self.logger.error(f"翻译异常: {str(e)}")
                translated_texts.append({
                    **text_info,
                    'translated': original_text,  # 保持原文
                    'from_cache': False,
                    'error': str(e)
                })
        
        # 生成输出文件路径
        if not output_file:
            input_path = Path(input_file)
            output_dir = TEST_OUTPUT_DIR if test_mode else FINAL_OUTPUT_DIR
            output_file = os.path.join(output_dir, f"{input_path.stem}.rpy")
        
        # 格式化并保存翻译文件
        try:
            self.formatter.format_and_save(
                translated_texts, 
                output_file, 
                original_file=input_file
            )
            self.logger.info(f"翻译文件已保存: {output_file}")
        except Exception as e:
            self.logger.error(f"保存翻译文件失败: {str(e)}")
            return {"success": False, "error": str(e)}
        
        # 返回统计信息
        return {
            "success": True,
            "input_file": input_file,
            "output_file": output_file,
            "total_texts": len(texts),
            "translated_count": success_count,
            "success_rate": (success_count / len(texts)) * 100 if texts else 0
        }
    
    def translate_directory(self, input_dir, output_dir=None, test_mode=True):
        """
        批量翻译目录中的所有.rpy文件
        
        Args:
            input_dir: 输入目录路径
            output_dir: 输出目录路径（可选）
            test_mode: 是否为测试模式
            
        Returns:
            批量翻译结果统计
        """
        self.logger.info(f"开始批量翻译目录: {input_dir}")
        
        # 查找所有.rpy文件
        input_path = Path(input_dir)
        rpy_files = list(input_path.glob("**/*.rpy"))
        
        if not rpy_files:
            self.logger.warning(f"在目录 {input_dir} 中没有找到.rpy文件")
            return {"success": True, "files": []}
        
        self.logger.info(f"找到 {len(rpy_files)} 个.rpy文件")
        
        # 翻译每个文件
        results = []
        for rpy_file in rpy_files:
            self.logger.info(f"处理文件: {rpy_file}")
            
            # 生成输出文件路径
            if output_dir:
                relative_path = rpy_file.relative_to(input_path)
                output_file = Path(output_dir) / relative_path
                output_file.parent.mkdir(parents=True, exist_ok=True)
            else:
                output_file = None
            
            # 翻译文件
            result = self.translate_file(str(rpy_file), str(output_file) if output_file else None, test_mode)
            results.append(result)
            
            self.logger.info("-" * 50)
        
        # 计算总体统计
        total_files = len(results)
        successful_files = sum(1 for r in results if r.get('success', False))
        total_texts = sum(r.get('total_texts', 0) for r in results)
        total_translated = sum(r.get('translated_count', 0) for r in results)
        
        summary = {
            "success": True,
            "total_files": total_files,
            "successful_files": successful_files,
            "total_texts": total_texts,
            "total_translated": total_translated,
            "overall_success_rate": (total_translated / total_texts) * 100 if total_texts else 0,
            "files": results
        }
        
        self.logger.info(f"批量翻译完成: {successful_files}/{total_files} 文件成功")
        self.logger.info(f"总体翻译成功率: {summary['overall_success_rate']:.2f}%")
        
        return summary


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Ren'Py游戏汉化工具 - 使用DeepSeek API进行翻译"
    )
    parser.add_argument(
        "input_path", 
        help="输入文件或目录路径"
    )
    parser.add_argument(
        "--output", "-o",
        help="输出文件或目录路径"
    )
    parser.add_argument(
        "--api-key",
        help="DeepSeek API密钥（覆盖配置文件中的设置）"
    )
    parser.add_argument(
        "--test-mode", "-t",
        action="store_true",
        default=True,
        help="测试模式（默认启用）"
    )
    parser.add_argument(
        "--final-mode", "-f",
        action="store_true",
        help="最终模式（禁用测试模式）"
    )
    parser.add_argument(
        "--log-file",
        help="日志文件路径"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logger = setup_logger(
        log_file=args.log_file,
        level=log_level
    )
    
    # 确定测试模式
    test_mode = args.test_mode and not args.final_mode
    
    try:
        # 初始化翻译工具
        tool = RenpyTranslationTool(
            api_key=args.api_key,
            logger=logger
        )
        
        # 检查输入路径
        input_path = Path(args.input_path)
        if not input_path.exists():
            logger.error(f"输入路径不存在: {args.input_path}")
            sys.exit(1)
        
        # 执行翻译
        if input_path.is_file():
            # 单文件翻译
            result = tool.translate_file(
                str(input_path),
                args.output,
                test_mode=test_mode
            )
            
            if result['success']:
                logger.info("翻译完成！")
                logger.info(f"成功率: {result.get('success_rate', 0):.2f}%")
            else:
                logger.error(f"翻译失败: {result.get('error', '未知错误')}")
                sys.exit(1)
                
        elif input_path.is_dir():
            # 目录批量翻译
            result = tool.translate_directory(
                str(input_path),
                args.output,
                test_mode=test_mode
            )
            
            if result['success']:
                logger.info("批量翻译完成！")
                logger.info(f"总体成功率: {result.get('overall_success_rate', 0):.2f}%")
            else:
                logger.error("批量翻译失败")
                sys.exit(1)
        else:
            logger.error(f"无效的输入路径: {args.input_path}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序异常: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
