#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ren'Py翻译工具测试脚本
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from parser import RenpyParser
from translator import DeepSeekTranslator
from formatter import RenpyFormatter
from utils import setup_logger, TranslationCache


def create_test_rpy_file():
    """创建测试用的.rpy文件"""
    content = '''label test_scene:
    
    scene bg room
    
    "This is a narration text that should be translated."
    
    show character happy
    
    c "Hello, this is a dialogue that needs translation."
    
    n "Another character speaking here."
    
    menu:
        "What should we do?"
        
        "Go to the park":
            jump park_scene
            
        "Stay at home":
            jump home_scene
    
    "The end of the scene."
    
    return
'''
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.rpy', delete=False, encoding='utf-8') as f:
        f.write(content)
        return f.name


def test_parser():
    """测试解析器"""
    print("=" * 50)
    print("测试解析器")
    print("=" * 50)
    
    # 创建测试文件
    test_file = create_test_rpy_file()
    print(f"创建测试文件: {test_file}")
    
    try:
        # 初始化解析器
        logger = setup_logger("test_parser")
        parser = RenpyParser(logger=logger)
        
        # 解析文件
        texts = parser.parse_file(test_file)
        
        print(f"解析结果: 找到 {len(texts)} 个需要翻译的文本")
        
        for i, text_info in enumerate(texts):
            print(f"{i+1}. [{text_info['type']}] {text_info['text']}")
        
        print("解析器测试通过!")
        return True
        
    except Exception as e:
        print(f"解析器测试失败: {str(e)}")
        return False
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.unlink(test_file)


def test_formatter():
    """测试格式化器"""
    print("=" * 50)
    print("测试格式化器")
    print("=" * 50)
    
    try:
        # 创建测试数据
        test_texts = [
            {
                'type': 'narration',
                'text': 'This is a test narration.',
                'translated': '这是一个测试旁白。',
                'line_number': 5
            },
            {
                'type': 'dialogue',
                'text': 'Hello world!',
                'translated': '你好世界！',
                'line_number': 8,
                'character': 'c'
            },
            {
                'type': 'menu_choice',
                'text': 'Choose option',
                'translated': '选择选项',
                'line_number': 12
            }
        ]
        
        # 初始化格式化器
        logger = setup_logger("test_formatter")
        formatter = RenpyFormatter(logger=logger)
        
        # 创建临时输出文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.rpy', delete=False) as f:
            output_file = f.name
        
        # 格式化并保存
        formatter.format_and_save(test_texts, output_file, "test_scene.rpy")
        
        # 读取并显示结果
        with open(output_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("生成的翻译文件内容:")
        print("-" * 30)
        print(content)
        print("-" * 30)
        
        # 验证格式
        is_valid = formatter.validate_translation_file(output_file)
        print(f"格式验证: {'通过' if is_valid else '失败'}")
        
        print("格式化器测试通过!")
        return True
        
    except Exception as e:
        print(f"格式化器测试失败: {str(e)}")
        return False
    
    finally:
        # 清理测试文件
        if 'output_file' in locals() and os.path.exists(output_file):
            os.unlink(output_file)


def test_cache():
    """测试缓存系统"""
    print("=" * 50)
    print("测试缓存系统")
    print("=" * 50)
    
    try:
        # 创建临时缓存文件
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            cache_file = f.name
        
        # 初始化缓存
        cache = TranslationCache(cache_file)
        
        # 测试设置和获取
        test_text = "Hello world"
        test_translation = "你好世界"
        
        # 设置缓存
        cache.set(test_text, test_translation)
        print(f"设置缓存: '{test_text}' -> '{test_translation}'")
        
        # 获取缓存
        cached_result = cache.get(test_text)
        print(f"获取缓存: '{cached_result}'")
        
        # 验证结果
        if cached_result == test_translation:
            print("缓存测试通过!")
            return True
        else:
            print("缓存测试失败: 结果不匹配")
            return False
        
    except Exception as e:
        print(f"缓存测试失败: {str(e)}")
        return False
    
    finally:
        # 清理测试文件
        if 'cache_file' in locals() and os.path.exists(cache_file):
            os.unlink(cache_file)


def test_translator_connection():
    """测试翻译器连接（不进行实际翻译）"""
    print("=" * 50)
    print("测试翻译器连接")
    print("=" * 50)
    
    try:
        # 从main.py导入API密钥
        import main
        api_key = main.DEEPSEEK_API_KEY
        
        if not api_key or api_key == "your-api-key-here":
            print("警告: 未配置有效的API密钥，跳过连接测试")
            return True
        
        # 初始化翻译器
        logger = setup_logger("test_translator")
        translator = DeepSeekTranslator(api_key=api_key, logger=logger)
        
        print("翻译器初始化成功")
        print("注意: 实际的API连接测试需要有效的API密钥和网络连接")
        
        return True
        
    except Exception as e:
        print(f"翻译器测试失败: {str(e)}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("开始运行Ren'Py翻译工具测试套件")
    print("=" * 60)
    
    tests = [
        ("解析器", test_parser),
        ("格式化器", test_formatter),
        ("缓存系统", test_cache),
        ("翻译器连接", test_translator_connection),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n正在测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 时发生异常: {str(e)}")
            results.append((test_name, False))
    
    # 显示测试结果摘要
    print("\n" + "=" * 60)
    print("测试结果摘要")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！工具可以正常使用。")
        return True
    else:
        print("❌ 部分测试失败，请检查相关组件。")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
