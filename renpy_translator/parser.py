#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ren'Py文件解析器
解析.rpy文件，提取需要翻译的文本
"""

import re
import logging
from typing import List, Dict, Any


class RenpyParser:
    """Ren'Py文件解析器"""
    
    def __init__(self, logger=None):
        """
        初始化解析器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 需要翻译的文本模式
        self.patterns = {
            # 对话文本：角色名 "对话内容"
            'dialogue': re.compile(r'^(\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s+"([^"]*)"', re.MULTILINE),
            
            # 旁白文本：直接的引号字符串
            'narration': re.compile(r'^(\s*)"([^"]*)"', re.MULTILINE),
            
            # 菜单选择项："选择内容":
            'menu_choice': re.compile(r'^(\s*)"([^"]*)"\s*:', re.MULTILINE),
            
            # 字符串标记：_("文本")
            'marked_string': re.compile(r'_\("([^"]*)"\)'),
        }
        
        # 不需要翻译的内容模式
        self.exclude_patterns = {
            # 音频播放指令
            'audio': re.compile(r'(play\s+music|play\s+sound|stop\s+music|stop\s+sound)'),
            
            # 图像显示指令
            'image': re.compile(r'(show\s+|scene\s+|hide\s+)'),
            
            # 特效指令
            'effects': re.compile(r'with\s+(fade|dissolve|movein|ease)'),
            
            # Python代码块
            'python': re.compile(r'^\s*\$'),
            
            # 注释
            'comment': re.compile(r'^\s*#'),
            
            # 标签定义
            'label': re.compile(r'^\s*label\s+'),
            
            # 变量赋值
            'assignment': re.compile(r'^\s*\$\s*\w+\s*='),
            
            # 音频文件路径
            'audio_file': re.compile(r'sounds/.*\.mp3'),
            
            # 空行
            'empty': re.compile(r'^\s*$'),
        }
    
    def should_exclude_line(self, line: str) -> bool:
        """
        检查是否应该排除某行
        
        Args:
            line: 文本行
            
        Returns:
            是否应该排除
        """
        for pattern_name, pattern in self.exclude_patterns.items():
            if pattern.search(line):
                self.logger.debug(f"排除行 ({pattern_name}): {line.strip()}")
                return True
        return False
    
    def extract_dialogue(self, content: str) -> List[Dict[str, Any]]:
        """
        提取对话文本
        
        Args:
            content: 文件内容
            
        Returns:
            对话文本列表
        """
        dialogues = []
        
        for match in self.patterns['dialogue'].finditer(content):
            indent = match.group(1)
            character = match.group(2)
            text = match.group(3)
            
            # 跳过空文本
            if not text.strip():
                continue
            
            dialogues.append({
                'type': 'dialogue',
                'character': character,
                'text': text,
                'indent': indent,
                'line_number': content[:match.start()].count('\n') + 1,
                'original_match': match.group(0)
            })
        
        return dialogues
    
    def extract_narration(self, content: str) -> List[Dict[str, Any]]:
        """
        提取旁白文本
        
        Args:
            content: 文件内容
            
        Returns:
            旁白文本列表
        """
        narrations = []
        
        for match in self.patterns['narration'].finditer(content):
            indent = match.group(1)
            text = match.group(2)
            
            # 跳过空文本
            if not text.strip():
                continue
            
            # 检查是否是对话（有角色名）
            line_start = content.rfind('\n', 0, match.start()) + 1
            line_content = content[line_start:match.start()]
            
            # 如果前面有角色名，则跳过（这是对话，不是旁白）
            if re.search(r'[a-zA-Z_][a-zA-Z0-9_]*\s*$', line_content):
                continue
            
            narrations.append({
                'type': 'narration',
                'text': text,
                'indent': indent,
                'line_number': content[:match.start()].count('\n') + 1,
                'original_match': match.group(0)
            })
        
        return narrations
    
    def extract_menu_choices(self, content: str) -> List[Dict[str, Any]]:
        """
        提取菜单选择项
        
        Args:
            content: 文件内容
            
        Returns:
            菜单选择项列表
        """
        choices = []
        
        for match in self.patterns['menu_choice'].finditer(content):
            indent = match.group(1)
            text = match.group(2)
            
            # 跳过空文本
            if not text.strip():
                continue
            
            choices.append({
                'type': 'menu_choice',
                'text': text,
                'indent': indent,
                'line_number': content[:match.start()].count('\n') + 1,
                'original_match': match.group(0)
            })
        
        return choices
    
    def extract_marked_strings(self, content: str) -> List[Dict[str, Any]]:
        """
        提取标记的字符串
        
        Args:
            content: 文件内容
            
        Returns:
            标记字符串列表
        """
        strings = []
        
        for match in self.patterns['marked_string'].finditer(content):
            text = match.group(1)
            
            # 跳过空文本
            if not text.strip():
                continue
            
            strings.append({
                'type': 'marked_string',
                'text': text,
                'line_number': content[:match.start()].count('\n') + 1,
                'original_match': match.group(0)
            })
        
        return strings
    
    def parse_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        解析Ren'Py文件，提取所有需要翻译的文本
        
        Args:
            file_path: 文件路径
            
        Returns:
            需要翻译的文本列表
        """
        self.logger.info(f"解析文件: {file_path}")
        
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            self.logger.error(f"读取文件失败: {str(e)}")
            raise
        
        # 提取各种类型的文本
        all_texts = []
        
        # 提取对话
        dialogues = self.extract_dialogue(content)
        all_texts.extend(dialogues)
        self.logger.debug(f"提取到 {len(dialogues)} 个对话")
        
        # 提取旁白
        narrations = self.extract_narration(content)
        all_texts.extend(narrations)
        self.logger.debug(f"提取到 {len(narrations)} 个旁白")
        
        # 提取菜单选择项
        choices = self.extract_menu_choices(content)
        all_texts.extend(choices)
        self.logger.debug(f"提取到 {len(choices)} 个菜单选择项")
        
        # 提取标记字符串
        marked = self.extract_marked_strings(content)
        all_texts.extend(marked)
        self.logger.debug(f"提取到 {len(marked)} 个标记字符串")
        
        # 按行号排序
        all_texts.sort(key=lambda x: x['line_number'])
        
        # 去重（基于文本内容）
        seen_texts = set()
        unique_texts = []
        
        for text_info in all_texts:
            text = text_info['text']
            if text not in seen_texts:
                seen_texts.add(text)
                unique_texts.append(text_info)
            else:
                self.logger.debug(f"跳过重复文本: {text}")
        
        self.logger.info(f"解析完成，共提取到 {len(unique_texts)} 个唯一文本")
        
        return unique_texts
    
    def parse_content(self, content: str) -> List[Dict[str, Any]]:
        """
        解析文本内容，提取所有需要翻译的文本
        
        Args:
            content: 文本内容
            
        Returns:
            需要翻译的文本列表
        """
        # 提取各种类型的文本
        all_texts = []
        
        # 提取对话
        dialogues = self.extract_dialogue(content)
        all_texts.extend(dialogues)
        
        # 提取旁白
        narrations = self.extract_narration(content)
        all_texts.extend(narrations)
        
        # 提取菜单选择项
        choices = self.extract_menu_choices(content)
        all_texts.extend(choices)
        
        # 提取标记字符串
        marked = self.extract_marked_strings(content)
        all_texts.extend(marked)
        
        # 按行号排序
        all_texts.sort(key=lambda x: x['line_number'])
        
        # 去重（基于文本内容）
        seen_texts = set()
        unique_texts = []
        
        for text_info in all_texts:
            text = text_info['text']
            if text not in seen_texts:
                seen_texts.add(text)
                unique_texts.append(text_info)
        
        return unique_texts
