#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量翻译脚本
一键翻译整个Ren'Py游戏的所有场景文件
"""

import os
import sys
import time
import argparse
from pathlib import Path
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import RenpyTranslationTool
from utils import setup_logger, StatisticsCollector, format_duration


def find_game_directory():
    """自动查找游戏目录"""
    current_dir = Path.cwd()
    
    # 检查当前目录的父目录是否包含game文件夹
    parent_dir = current_dir.parent
    game_dir = parent_dir / "game"
    
    if game_dir.exists() and (game_dir / "scenes").exists():
        return str(game_dir / "scenes")
    
    # 检查当前目录是否包含game文件夹
    game_dir = current_dir / "game"
    if game_dir.exists() and (game_dir / "scenes").exists():
        return str(game_dir / "scenes")
    
    return None


def estimate_translation_time(scene_count, avg_texts_per_scene=15):
    """估算翻译时间"""
    # 假设每个文本翻译需要3-5秒
    total_texts = scene_count * avg_texts_per_scene
    estimated_seconds = total_texts * 4  # 平均4秒每个文本
    return estimated_seconds


def create_progress_report(stats, output_file):
    """创建进度报告"""
    report = f"""# Ren'Py游戏翻译报告

## 翻译概览
- 开始时间: {stats['start_time'].strftime('%Y-%m-%d %H:%M:%S')}
- 结束时间: {stats['end_time'].strftime('%Y-%m-%d %H:%M:%S') if stats['end_time'] else '进行中'}
- 总耗时: {format_duration(stats.get('duration_seconds', 0))}

## 文件统计
- 处理文件数: {stats['files_processed']}
- 成功文件数: {stats['files_successful']}
- 文件成功率: {(stats['files_successful']/max(stats['files_processed'], 1)*100):.1f}%

## 翻译统计
- 总文本数: {stats['texts_total']}
- 翻译成功数: {stats['texts_translated']}
- 缓存命中数: {stats['texts_cached']}
- 翻译成功率: {stats.get('success_rate', 0):.1f}%
- 缓存命中率: {stats.get('cache_hit_rate', 0):.1f}%

## API统计
- API调用次数: {stats['api_calls']}
- API失败次数: {stats['api_failures']}
- API成功率: {stats.get('api_success_rate', 0):.1f}%

## 性能指标
- 平均每文件耗时: {stats.get('duration_seconds', 0)/max(stats['files_processed'], 1):.1f}秒
- 平均每文本耗时: {stats.get('duration_seconds', 0)/max(stats['texts_total'], 1):.1f}秒
"""
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="批量翻译Ren'Py游戏场景文件"
    )
    parser.add_argument(
        "--input-dir", "-i",
        help="输入目录路径（默认自动查找game/scenes目录）"
    )
    parser.add_argument(
        "--output-dir", "-o",
        default="output/final",
        help="输出目录路径（默认: output/final）"
    )
    parser.add_argument(
        "--api-key",
        help="DeepSeek API密钥（覆盖配置文件设置）"
    )
    parser.add_argument(
        "--test-mode", "-t",
        action="store_true",
        help="测试模式（输出到output/test目录）"
    )
    parser.add_argument(
        "--resume", "-r",
        action="store_true",
        help="恢复模式（跳过已存在的翻译文件）"
    )
    parser.add_argument(
        "--max-files", "-m",
        type=int,
        help="最大处理文件数（用于测试）"
    )
    parser.add_argument(
        "--log-file",
        help="日志文件路径"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    log_level = 10 if args.verbose else 20  # DEBUG : INFO
    if not args.log_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.log_file = f"batch_translation_{timestamp}.log"
    
    logger = setup_logger(
        name="batch_translator",
        log_file=args.log_file,
        level=log_level
    )
    
    print("🎮 Ren'Py游戏批量翻译工具")
    print("=" * 50)
    
    try:
        # 查找输入目录
        if args.input_dir:
            input_dir = args.input_dir
        else:
            input_dir = find_game_directory()
            if not input_dir:
                print("❌ 无法找到游戏目录，请使用 --input-dir 指定")
                sys.exit(1)
        
        if not os.path.exists(input_dir):
            print(f"❌ 输入目录不存在: {input_dir}")
            sys.exit(1)
        
        print(f"📁 输入目录: {input_dir}")
        
        # 设置输出目录
        if args.test_mode:
            output_dir = "output/test"
        else:
            output_dir = args.output_dir
        
        print(f"📁 输出目录: {output_dir}")
        
        # 查找所有.rpy文件
        input_path = Path(input_dir)
        rpy_files = list(input_path.glob("*.rpy"))
        
        if not rpy_files:
            print(f"❌ 在目录 {input_dir} 中没有找到.rpy文件")
            sys.exit(1)
        
        # 按文件名排序
        rpy_files.sort(key=lambda x: x.name)
        
        # 限制文件数量（如果指定）
        if args.max_files:
            rpy_files = rpy_files[:args.max_files]
        
        print(f"📋 找到 {len(rpy_files)} 个场景文件")
        
        # 估算翻译时间
        estimated_time = estimate_translation_time(len(rpy_files))
        print(f"⏱️  预计翻译时间: {format_duration(estimated_time)}")
        
        # 确认开始
        if not args.test_mode:
            response = input("\n是否开始批量翻译？(y/N): ")
            if response.lower() != 'y':
                print("取消翻译")
                sys.exit(0)
        
        print("\n🚀 开始批量翻译...")
        print("=" * 50)
        
        # 初始化翻译工具
        tool = RenpyTranslationTool(
            api_key=args.api_key,
            logger=logger
        )
        
        # 初始化统计收集器
        stats = StatisticsCollector()
        
        # 处理每个文件
        start_time = time.time()
        
        for i, rpy_file in enumerate(rpy_files):
            print(f"\n📄 [{i+1}/{len(rpy_files)}] 处理文件: {rpy_file.name}")
            
            # 生成输出文件路径
            output_file = Path(output_dir) / f"{rpy_file.stem}.rpy"
            
            # 检查是否需要跳过（恢复模式）
            if args.resume and output_file.exists():
                print(f"   ⏭️  跳过已存在的文件: {output_file}")
                continue
            
            # 翻译文件
            try:
                result = tool.translate_file(
                    str(rpy_file),
                    str(output_file),
                    test_mode=args.test_mode
                )
                
                # 记录统计信息
                stats.add_file_result(result)
                
                if result['success']:
                    success_rate = result.get('success_rate', 0)
                    print(f"   ✅ 翻译完成 - 成功率: {success_rate:.1f}%")
                else:
                    print(f"   ❌ 翻译失败: {result.get('error', '未知错误')}")
                
            except KeyboardInterrupt:
                print("\n⚠️  用户中断翻译")
                break
            except Exception as e:
                print(f"   ❌ 处理文件时出错: {str(e)}")
                logger.error(f"处理文件 {rpy_file} 时出错: {str(e)}")
                continue
        
        # 完成统计
        stats.finish()
        elapsed_time = time.time() - start_time
        
        # 显示最终统计
        print("\n" + "=" * 50)
        print("🎉 批量翻译完成!")
        print("=" * 50)
        
        summary = stats.get_summary()
        print(f"📊 处理文件: {summary['files_successful']}/{summary['files_processed']}")
        print(f"📊 翻译文本: {summary['texts_translated']}/{summary['texts_total']}")
        print(f"📊 总体成功率: {summary['success_rate']:.1f}%")
        print(f"📊 缓存命中率: {summary['cache_hit_rate']:.1f}%")
        print(f"📊 总耗时: {format_duration(elapsed_time)}")
        
        # 保存详细报告
        report_file = f"translation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        create_progress_report(summary, report_file)
        print(f"📋 详细报告已保存: {report_file}")
        
        # 保存统计数据
        stats_file = f"translation_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        stats.save_report(stats_file)
        print(f"📊 统计数据已保存: {stats_file}")
        
        print(f"\n📁 翻译文件位置: {output_dir}/")
        
        if not args.test_mode:
            print("\n📝 下一步:")
            print("1. 检查翻译文件质量")
            print("2. 将翻译文件复制到游戏的 game/tl/chinese/scenes/ 目录")
            print("3. 启动游戏测试翻译效果")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(0)
    except Exception as e:
        logger.error(f"批量翻译失败: {str(e)}")
        print(f"❌ 批量翻译失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
