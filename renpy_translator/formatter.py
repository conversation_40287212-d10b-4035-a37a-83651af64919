#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ren'Py翻译文件格式化器
生成符合Ren'Py规范的翻译文件
"""

import os
import re
import hashlib
import logging
from typing import List, Dict, Any
from pathlib import Path


class RenpyFormatter:
    """Ren'Py翻译文件格式化器"""
    
    def __init__(self, logger=None):
        """
        初始化格式化器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
    
    def escape_quotes(self, text: str) -> str:
        """
        转义字符串中的引号
        
        Args:
            text: 原始文本
            
        Returns:
            转义后的文本
        """
        # 转义双引号
        text = text.replace('"', '\\"')
        
        # 处理单引号（撇号）
        text = text.replace("'", "\\'")
        
        return text
    
    def generate_label_hash(self, original_file: str, text: str) -> str:
        """
        生成翻译标签的哈希值
        
        Args:
            original_file: 原始文件路径
            text: 文本内容
            
        Returns:
            哈希值
        """
        # 使用文件名和文本内容生成哈希
        content = f"{original_file}:{text}"
        hash_obj = hashlib.md5(content.encode('utf-8'))
        return hash_obj.hexdigest()[:8]
    
    def format_translation_block(self, texts: List[Dict[str, Any]], original_file: str) -> str:
        """
        格式化翻译块
        
        Args:
            texts: 文本列表
            original_file: 原始文件路径
            
        Returns:
            格式化的翻译块
        """
        if not texts:
            return ""
        
        # 生成标签名
        file_stem = Path(original_file).stem
        label_name = file_stem.replace('.', '_').replace('-', '_')
        
        # 开始翻译块
        lines = [f"translate chinese {label_name}:"]
        lines.append("")
        
        # 添加每个翻译项
        for text_info in texts:
            original_text = text_info['text']
            translated_text = text_info.get('translated', original_text)
            
            # 转义引号
            escaped_original = self.escape_quotes(original_text)
            escaped_translated = self.escape_quotes(translated_text)
            
            # 添加注释（可选）
            if 'line_number' in text_info:
                lines.append(f"    # Line {text_info['line_number']}: {text_info.get('type', 'text')}")
            
            # 添加old/new语句
            lines.append(f'    old "{escaped_original}"')
            lines.append(f'    new "{escaped_translated}"')
            lines.append("")
        
        return "\n".join(lines)
    
    def format_strings_block(self, texts: List[Dict[str, Any]]) -> str:
        """
        格式化字符串翻译块
        
        Args:
            texts: 文本列表
            
        Returns:
            格式化的字符串翻译块
        """
        if not texts:
            return ""
        
        lines = ["translate chinese strings:"]
        lines.append("")
        
        # 添加每个翻译项
        for text_info in texts:
            original_text = text_info['text']
            translated_text = text_info.get('translated', original_text)
            
            # 转义引号
            escaped_original = self.escape_quotes(original_text)
            escaped_translated = self.escape_quotes(translated_text)
            
            # 添加注释（可选）
            if 'line_number' in text_info:
                lines.append(f"    # Line {text_info['line_number']}: {text_info.get('type', 'text')}")
            
            # 添加old/new语句
            lines.append(f'    old "{escaped_original}"')
            lines.append(f'    new "{escaped_translated}"')
            lines.append("")
        
        return "\n".join(lines)
    
    def categorize_texts(self, texts: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        将文本按类型分类
        
        Args:
            texts: 文本列表
            
        Returns:
            分类后的文本字典
        """
        categories = {
            'dialogue': [],
            'narration': [],
            'menu_choice': [],
            'marked_string': [],
            'other': []
        }
        
        for text_info in texts:
            text_type = text_info.get('type', 'other')
            if text_type in categories:
                categories[text_type].append(text_info)
            else:
                categories['other'].append(text_info)
        
        return categories
    
    def format_and_save(self, texts: List[Dict[str, Any]], output_file: str, original_file: str = None):
        """
        格式化并保存翻译文件
        
        Args:
            texts: 翻译文本列表
            output_file: 输出文件路径
            original_file: 原始文件路径
        """
        self.logger.info(f"格式化翻译文件: {output_file}")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 生成文件头部注释
        lines = [
            "# -*- coding: utf-8 -*-",
            f"# Ren'Py翻译文件",
            f"# 原始文件: {original_file or 'Unknown'}",
            f"# 生成时间: {self.get_timestamp()}",
            f"# 翻译项数量: {len(texts)}",
            "",
        ]
        
        if not texts:
            lines.append("# 没有找到需要翻译的文本")
            content = "\n".join(lines)
        else:
            # 分类文本
            categories = self.categorize_texts(texts)
            
            # 检查是否有标记字符串（界面文本）
            if categories['marked_string']:
                # 使用strings格式
                strings_block = self.format_strings_block(categories['marked_string'])
                lines.append(strings_block)
                
                # 如果还有其他类型的文本，添加普通翻译块
                other_texts = []
                for category in ['dialogue', 'narration', 'menu_choice', 'other']:
                    other_texts.extend(categories[category])
                
                if other_texts:
                    lines.append("")
                    lines.append("# 对话和旁白翻译")
                    translation_block = self.format_translation_block(other_texts, original_file or "unknown")
                    lines.append(translation_block)
            else:
                # 使用普通翻译格式
                translation_block = self.format_translation_block(texts, original_file or "unknown")
                lines.append(translation_block)
            
            content = "\n".join(lines)
        
        # 写入文件
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.logger.info(f"翻译文件已保存: {output_file}")
            
        except Exception as e:
            self.logger.error(f"保存翻译文件失败: {str(e)}")
            raise
    
    def get_timestamp(self) -> str:
        """
        获取当前时间戳
        
        Returns:
            时间戳字符串
        """
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def validate_translation_file(self, file_path: str) -> bool:
        """
        验证翻译文件格式
        
        Args:
            file_path: 翻译文件路径
            
        Returns:
            是否格式正确
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查基本格式
            if not re.search(r'translate\s+chinese\s+\w+:', content):
                self.logger.error("缺少translate语句")
                return False
            
            # 检查old/new配对
            old_count = len(re.findall(r'^\s*old\s+"', content, re.MULTILINE))
            new_count = len(re.findall(r'^\s*new\s+"', content, re.MULTILINE))
            
            if old_count != new_count:
                self.logger.error(f"old/new语句不匹配: old={old_count}, new={new_count}")
                return False
            
            self.logger.info("翻译文件格式验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"验证翻译文件失败: {str(e)}")
            return False
    
    def fix_common_issues(self, file_path: str) -> bool:
        """
        修复常见的格式问题
        
        Args:
            file_path: 翻译文件路径
            
        Returns:
            是否修复成功
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            fixed_lines = []
            for line in lines:
                # 修复缩进问题
                if re.match(r'^\s*(old|new)\s+"', line):
                    # 确保正确的缩进（4个空格）
                    content = line.strip()
                    fixed_lines.append(f"    {content}")
                else:
                    fixed_lines.append(line.rstrip())
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(fixed_lines))
            
            self.logger.info(f"已修复文件格式: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"修复文件格式失败: {str(e)}")
            return False
