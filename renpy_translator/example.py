#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ren'Py翻译工具使用示例
演示如何使用翻译工具的各个组件
"""

import os
import sys
import tempfile

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import RenpyTranslationTool
from utils import setup_logger


def create_sample_rpy_file():
    """创建示例.rpy文件"""
    content = '''# Sample Ren'Py scene file
label sample_scene:
    
    scene bg bedroom
    
    "It was a quiet morning in the small apartment."
    
    show natalia happy at center
    
    n "Good morning, honey! Did you sleep well?"
    
    show ethan sleepy at right
    
    e "Yeah, I had a great sleep. Thanks for asking."
    
    n "I was thinking we could go out for breakfast today."
    
    menu:
        "What would you like to do?"
        
        "Go to the cafe":
            $ choice_cafe = True
            jump cafe_scene
            
        "Cook at home":
            $ choice_home = True
            jump cooking_scene
    
    "They spent the morning together, enjoying each other's company."
    
    return

label cafe_scene:
    scene bg cafe
    "They went to their favorite cafe downtown."
    return

label cooking_scene:
    scene bg kitchen
    "They decided to cook breakfast together at home."
    return
'''
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.rpy', delete=False, encoding='utf-8') as f:
        f.write(content)
        return f.name


def example_single_file_translation():
    """示例：翻译单个文件"""
    print("=" * 60)
    print("示例1: 翻译单个文件")
    print("=" * 60)
    
    # 创建示例文件
    sample_file = create_sample_rpy_file()
    print(f"创建示例文件: {sample_file}")
    
    try:
        # 设置日志
        logger = setup_logger("example", level=20)  # INFO级别
        
        # 初始化翻译工具
        # 注意：这里使用模拟模式，不会实际调用API
        tool = RenpyTranslationTool(
            api_key="demo-key",  # 演示用密钥
            logger=logger
        )
        
        print("\n1. 解析文件...")
        texts = tool.parser.parse_file(sample_file)
        print(f"   找到 {len(texts)} 个需要翻译的文本:")
        
        for i, text_info in enumerate(texts[:5]):  # 只显示前5个
            print(f"   {i+1}. [{text_info['type']}] {text_info['text'][:50]}...")
        
        if len(texts) > 5:
            print(f"   ... 还有 {len(texts) - 5} 个文本")
        
        print("\n2. 模拟翻译过程...")
        # 这里我们模拟翻译结果，而不是实际调用API
        translated_texts = []
        for text_info in texts:
            # 简单的模拟翻译（实际使用时会调用DeepSeek API）
            mock_translation = f"[翻译] {text_info['text']}"
            translated_texts.append({
                **text_info,
                'translated': mock_translation
            })
        
        print(f"   模拟翻译了 {len(translated_texts)} 个文本")
        
        print("\n3. 生成翻译文件...")
        output_file = "output/test/sample_scene.rpy"
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        tool.formatter.format_and_save(
            translated_texts,
            output_file,
            sample_file
        )
        
        print(f"   翻译文件已保存: {output_file}")
        
        # 显示生成的文件内容（前20行）
        print("\n4. 生成的翻译文件内容（前20行）:")
        print("-" * 40)
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[:20]):
                print(f"{i+1:2d}: {line.rstrip()}")
            if len(lines) > 20:
                print(f"... 还有 {len(lines) - 20} 行")
        print("-" * 40)
        
        print("\n✅ 单文件翻译示例完成!")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {str(e)}")
    
    finally:
        # 清理示例文件
        if os.path.exists(sample_file):
            os.unlink(sample_file)


def example_cache_usage():
    """示例：缓存使用"""
    print("\n" + "=" * 60)
    print("示例2: 缓存系统使用")
    print("=" * 60)
    
    try:
        from utils import TranslationCache
        
        # 创建缓存实例
        cache = TranslationCache("example_cache.json")
        
        print("1. 设置缓存项...")
        test_translations = [
            ("Hello", "你好"),
            ("Good morning", "早上好"),
            ("Thank you", "谢谢"),
            ("How are you?", "你好吗？"),
            ("See you later", "再见")
        ]
        
        for original, translation in test_translations:
            cache.set(original, translation)
            print(f"   '{original}' -> '{translation}'")
        
        print(f"\n2. 缓存大小: {cache.size()} 项")
        
        print("\n3. 测试缓存查询...")
        test_queries = ["Hello", "Good morning", "Unknown text"]
        
        for query in test_queries:
            result = cache.get(query)
            if result:
                print(f"   '{query}' -> '{result}' ✅")
            else:
                print(f"   '{query}' -> 未找到 ❌")
        
        print("\n✅ 缓存系统示例完成!")
        
    except Exception as e:
        print(f"❌ 缓存示例失败: {str(e)}")


def example_batch_processing():
    """示例：批量处理"""
    print("\n" + "=" * 60)
    print("示例3: 批量处理演示")
    print("=" * 60)
    
    try:
        # 创建多个示例文件
        temp_dir = tempfile.mkdtemp()
        print(f"创建临时目录: {temp_dir}")
        
        sample_files = []
        for i in range(3):
            content = f'''label scene_{i+1}:
    "This is scene {i+1} narration."
    
    character "Hello from scene {i+1}!"
    
    menu:
        "Scene {i+1} choice"
        "Option A":
            pass
        "Option B":
            pass
    
    return
'''
            file_path = os.path.join(temp_dir, f"scene_{i+1}.rpy")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            sample_files.append(file_path)
        
        print(f"创建了 {len(sample_files)} 个示例文件")
        
        # 模拟批量处理
        from parser import RenpyParser
        logger = setup_logger("batch_example")
        parser = RenpyParser(logger=logger)
        
        total_texts = 0
        for file_path in sample_files:
            texts = parser.parse_file(file_path)
            total_texts += len(texts)
            print(f"   {os.path.basename(file_path)}: {len(texts)} 个文本")
        
        print(f"\n总计: {total_texts} 个需要翻译的文本")
        print("✅ 批量处理示例完成!")
        
    except Exception as e:
        print(f"❌ 批量处理示例失败: {str(e)}")
    
    finally:
        # 清理临时文件
        import shutil
        if 'temp_dir' in locals() and os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)


def example_format_validation():
    """示例：格式验证"""
    print("\n" + "=" * 60)
    print("示例4: 翻译文件格式验证")
    print("=" * 60)
    
    try:
        from formatter import RenpyFormatter
        
        # 创建格式化器
        logger = setup_logger("format_example")
        formatter = RenpyFormatter(logger=logger)
        
        # 创建一个正确格式的翻译文件
        correct_content = '''# -*- coding: utf-8 -*-
translate chinese test_scene:

    # Line 5: narration
    old "Hello world"
    new "你好世界"

    # Line 8: dialogue
    old "How are you?"
    new "你好吗？"

'''
        
        # 创建一个错误格式的翻译文件
        incorrect_content = '''translate chinese test_scene:
old "Hello world"
new "你好世界"
old "Missing new statement"
'''
        
        # 测试正确格式
        with tempfile.NamedTemporaryFile(mode='w', suffix='.rpy', delete=False, encoding='utf-8') as f:
            f.write(correct_content)
            correct_file = f.name
        
        # 测试错误格式
        with tempfile.NamedTemporaryFile(mode='w', suffix='.rpy', delete=False, encoding='utf-8') as f:
            f.write(incorrect_content)
            incorrect_file = f.name
        
        print("1. 验证正确格式的文件...")
        is_valid = formatter.validate_translation_file(correct_file)
        print(f"   结果: {'通过' if is_valid else '失败'} ✅" if is_valid else "   结果: 失败 ❌")
        
        print("\n2. 验证错误格式的文件...")
        is_valid = formatter.validate_translation_file(incorrect_file)
        print(f"   结果: {'通过' if is_valid else '失败'} ❌" if not is_valid else "   结果: 通过 ✅")
        
        print("\n✅ 格式验证示例完成!")
        
    except Exception as e:
        print(f"❌ 格式验证示例失败: {str(e)}")
    
    finally:
        # 清理测试文件
        for file_var in ['correct_file', 'incorrect_file']:
            if file_var in locals() and os.path.exists(locals()[file_var]):
                os.unlink(locals()[file_var])


def main():
    """运行所有示例"""
    print("🎮 Ren'Py翻译工具使用示例")
    print("=" * 60)
    print("本示例将演示翻译工具的主要功能")
    print("注意：为了演示目的，不会进行实际的API调用")
    print("=" * 60)
    
    try:
        # 确保输出目录存在
        os.makedirs("output/test", exist_ok=True)
        os.makedirs("output/final", exist_ok=True)
        
        # 运行各个示例
        example_single_file_translation()
        example_cache_usage()
        example_batch_processing()
        example_format_validation()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例运行完成!")
        print("=" * 60)
        print("\n📝 下一步:")
        print("1. 配置您的DeepSeek API密钥在main.py中")
        print("2. 运行: python main.py your_game_file.rpy")
        print("3. 检查output/test/目录中的翻译结果")
        print("4. 确认无误后使用--final-mode生成最终版本")
        print("5. 将翻译文件复制到游戏的tl/chinese/目录")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
