#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek API翻译器
使用DeepSeek API进行文本翻译
"""

import json
import time
import logging
import requests
from typing import Dict, Any, Optional


class DeepSeekTranslator:
    """DeepSeek API翻译器"""
    
    def __init__(self, api_key: str, api_url: str = None, logger=None):
        """
        初始化翻译器
        
        Args:
            api_key: DeepSeek API密钥
            api_url: API URL
            logger: 日志记录器
        """
        self.api_key = api_key
        self.api_url = api_url or "https://api.deepseek.com/v1/chat/completions"
        self.logger = logger or logging.getLogger(__name__)
        
        # 请求配置
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        # 翻译配置
        self.max_retries = 3
        self.retry_delay = 2
        self.timeout = 30
        
        # 翻译提示词
        self.system_prompt = """你是一个专业的游戏本地化翻译专家，专门负责将英文游戏文本翻译成中文。

翻译要求：
1. 保持原文的语气和情感色彩
2. 使用自然流畅的中文表达
3. 保留游戏中的专有名词（如角色名、地名等）
4. 保持原文的格式标记（如颜色代码、变量等）
5. 对于成人内容，使用合适的中文表达
6. 翻译要符合中文的表达习惯

请只返回翻译结果，不要添加任何解释或说明。"""
    
    def translate(self, text: str, source_lang: str = "en", target_lang: str = "zh") -> Dict[str, Any]:
        """
        翻译文本
        
        Args:
            text: 要翻译的文本
            source_lang: 源语言代码
            target_lang: 目标语言代码
            
        Returns:
            翻译结果字典
        """
        if not text.strip():
            return {
                "success": True,
                "translated_text": "",
                "message": "Empty text",
                "attempts": 0
            }
        
        self.logger.debug(f"准备翻译: {text[:50]}...")
        
        # 构建请求数据
        request_data = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "system",
                    "content": self.system_prompt
                },
                {
                    "role": "user",
                    "content": f"请将以下英文文本翻译成中文：\n\n{text}"
                }
            ],
            "temperature": 0.3,
            "max_tokens": 1000,
            "stream": False
        }
        
        # 重试机制
        for attempt in range(self.max_retries):
            try:
                self.logger.debug(f"尝试 {attempt + 1}/{self.max_retries}")
                
                # 发送请求
                response = requests.post(
                    self.api_url,
                    headers=self.headers,
                    json=request_data,
                    timeout=self.timeout
                )
                
                self.logger.debug(f"响应状态码: {response.status_code}")
                
                # 检查HTTP状态码
                if response.status_code != 200:
                    error_msg = f"HTTP错误 {response.status_code}: {response.text}"
                    self.logger.warning(error_msg)
                    
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                        continue
                    else:
                        return {
                            "success": False,
                            "translated_text": "",
                            "error": error_msg,
                            "attempts": attempt + 1
                        }
                
                # 解析响应
                try:
                    result = response.json()
                except json.JSONDecodeError as e:
                    error_msg = f"JSON解析失败: {str(e)}"
                    self.logger.error(error_msg)
                    
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                        continue
                    else:
                        return {
                            "success": False,
                            "translated_text": "",
                            "error": error_msg,
                            "attempts": attempt + 1
                        }
                
                # 检查API响应
                if "choices" not in result or not result["choices"]:
                    error_msg = "API响应中没有翻译结果"
                    self.logger.warning(error_msg)
                    
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                        continue
                    else:
                        return {
                            "success": False,
                            "translated_text": "",
                            "error": error_msg,
                            "attempts": attempt + 1
                        }
                
                # 提取翻译结果
                translated_text = result["choices"][0]["message"]["content"].strip()
                
                if not translated_text:
                    error_msg = "翻译结果为空"
                    self.logger.warning(error_msg)
                    
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                        continue
                    else:
                        return {
                            "success": False,
                            "translated_text": "",
                            "error": error_msg,
                            "attempts": attempt + 1
                        }
                
                # 翻译成功
                self.logger.debug(f"翻译成功: {translated_text[:50]}...")
                return {
                    "success": True,
                    "translated_text": translated_text,
                    "message": "Success",
                    "attempts": attempt + 1,
                    "usage": result.get("usage", {})
                }
                
            except requests.exceptions.Timeout:
                error_msg = "请求超时"
                self.logger.warning(f"尝试 {attempt + 1}/{self.max_retries} - {error_msg}")
                
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    continue
                else:
                    return {
                        "success": False,
                        "translated_text": "",
                        "error": error_msg,
                        "attempts": attempt + 1
                    }
                    
            except requests.exceptions.ConnectionError:
                error_msg = "连接错误"
                self.logger.warning(f"尝试 {attempt + 1}/{self.max_retries} - {error_msg}")
                
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    continue
                else:
                    return {
                        "success": False,
                        "translated_text": "",
                        "error": error_msg,
                        "attempts": attempt + 1
                    }
                    
            except Exception as e:
                error_msg = f"未知错误: {str(e)}"
                self.logger.error(f"尝试 {attempt + 1}/{self.max_retries} - {error_msg}")
                
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    continue
                else:
                    return {
                        "success": False,
                        "translated_text": "",
                        "error": error_msg,
                        "attempts": attempt + 1
                    }
        
        # 所有重试都失败
        return {
            "success": False,
            "translated_text": "",
            "error": "所有重试都失败",
            "attempts": self.max_retries
        }
    
    def translate_batch(self, texts: list, batch_size: int = 10) -> list:
        """
        批量翻译文本
        
        Args:
            texts: 文本列表
            batch_size: 批次大小
            
        Returns:
            翻译结果列表
        """
        results = []
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            self.logger.info(f"翻译批次 {i//batch_size + 1}: {len(batch)} 个文本")
            
            batch_results = []
            for text in batch:
                result = self.translate(text)
                batch_results.append(result)
                
                # 短暂延迟，避免请求过于频繁
                time.sleep(0.5)
            
            results.extend(batch_results)
        
        return results
    
    def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            连接是否成功
        """
        try:
            result = self.translate("Hello")
            return result["success"]
        except Exception as e:
            self.logger.error(f"连接测试失败: {str(e)}")
            return False
