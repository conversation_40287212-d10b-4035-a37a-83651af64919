#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
包含日志设置、缓存管理等工具函数
"""

import os
import json
import logging
import hashlib
from typing import Dict, Any, Optional
from datetime import datetime


def setup_logger(name: str = None, log_file: str = None, level: int = logging.INFO) -> logging.Logger:
    """
    设置日志记录器

    Args:
        name: 日志记录器名称
        log_file: 日志文件路径
        level: 日志级别

    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name or __name__)
    logger.setLevel(level)

    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 如果指定了日志文件，创建文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir:  # 只有当目录不为空时才创建
            os.makedirs(log_dir, exist_ok=True)

        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger


class TranslationCache:
    """翻译缓存管理器"""

    def __init__(self, cache_file: str = "translation_cache.json"):
        """
        初始化缓存管理器

        Args:
            cache_file: 缓存文件路径
        """
        self.cache_file = cache_file
        self.cache = {}
        self.load_cache()

    def _hash_text(self, text: str) -> str:
        """
        生成文本哈希值

        Args:
            text: 文本内容

        Returns:
            哈希值
        """
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def load_cache(self):
        """从文件加载缓存"""
        if os.path.exists(self.cache_file):
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    self.cache = json.load(f)
            except Exception as e:
                logging.warning(f"加载缓存文件失败: {str(e)}")
                self.cache = {}
        else:
            self.cache = {}

    def save_cache(self):
        """保存缓存到文件"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logging.error(f"保存缓存文件失败: {str(e)}")

    def get(self, text: str) -> Optional[str]:
        """
        获取翻译缓存

        Args:
            text: 原始文本

        Returns:
            翻译结果或None
        """
        text_hash = self._hash_text(text)
        return self.cache.get(text_hash)

    def set(self, text: str, translation: str):
        """
        设置翻译缓存

        Args:
            text: 原始文本
            translation: 翻译结果
        """
        text_hash = self._hash_text(text)
        self.cache[text_hash] = translation
        self.save_cache()

    def clear(self):
        """清空缓存"""
        self.cache = {}
        if os.path.exists(self.cache_file):
            os.remove(self.cache_file)

    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)


class ProgressTracker:
    """进度跟踪器"""

    def __init__(self, total: int, logger=None):
        """
        初始化进度跟踪器

        Args:
            total: 总数量
            logger: 日志记录器
        """
        self.total = total
        self.current = 0
        self.logger = logger or logging.getLogger(__name__)
        self.start_time = datetime.now()

    def update(self, increment: int = 1):
        """
        更新进度

        Args:
            increment: 增量
        """
        self.current += increment

        # 计算进度百分比
        percentage = (self.current / self.total) * 100 if self.total > 0 else 0

        # 计算预估剩余时间
        elapsed = datetime.now() - self.start_time
        if self.current > 0:
            avg_time_per_item = elapsed.total_seconds() / self.current
            remaining_items = self.total - self.current
            estimated_remaining = avg_time_per_item * remaining_items

            self.logger.info(
                f"进度: {self.current}/{self.total} ({percentage:.1f}%) "
                f"- 预计剩余: {estimated_remaining:.0f}秒"
            )
        else:
            self.logger.info(f"进度: {self.current}/{self.total} ({percentage:.1f}%)")

    def finish(self):
        """完成进度跟踪"""
        elapsed = datetime.now() - self.start_time
        self.logger.info(f"完成! 总耗时: {elapsed.total_seconds():.1f}秒")


class FileValidator:
    """文件验证器"""

    @staticmethod
    def is_rpy_file(file_path: str) -> bool:
        """
        检查是否为.rpy文件

        Args:
            file_path: 文件路径

        Returns:
            是否为.rpy文件
        """
        return file_path.lower().endswith('.rpy')

    @staticmethod
    def is_readable(file_path: str) -> bool:
        """
        检查文件是否可读

        Args:
            file_path: 文件路径

        Returns:
            是否可读
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                f.read(1)  # 尝试读取一个字符
            return True
        except Exception:
            return False

    @staticmethod
    def validate_encoding(file_path: str) -> str:
        """
        验证文件编码

        Args:
            file_path: 文件路径

        Returns:
            文件编码
        """
        import chardet

        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()

            result = chardet.detect(raw_data)
            return result.get('encoding', 'utf-8')
        except Exception:
            return 'utf-8'


class StatisticsCollector:
    """统计信息收集器"""

    def __init__(self):
        """初始化统计收集器"""
        self.stats = {
            'files_processed': 0,
            'files_successful': 0,
            'texts_total': 0,
            'texts_translated': 0,
            'texts_cached': 0,
            'api_calls': 0,
            'api_failures': 0,
            'start_time': datetime.now(),
            'end_time': None
        }

    def add_file_result(self, result: Dict[str, Any]):
        """
        添加文件处理结果

        Args:
            result: 文件处理结果
        """
        self.stats['files_processed'] += 1

        if result.get('success', False):
            self.stats['files_successful'] += 1
            self.stats['texts_total'] += result.get('total_texts', 0)
            self.stats['texts_translated'] += result.get('translated_count', 0)

    def add_translation_result(self, result: Dict[str, Any]):
        """
        添加翻译结果

        Args:
            result: 翻译结果
        """
        self.stats['api_calls'] += 1

        if not result.get('success', False):
            self.stats['api_failures'] += 1

    def add_cache_hit(self):
        """添加缓存命中"""
        self.stats['texts_cached'] += 1

    def finish(self):
        """完成统计"""
        self.stats['end_time'] = datetime.now()

    def get_summary(self) -> Dict[str, Any]:
        """
        获取统计摘要

        Returns:
            统计摘要
        """
        if self.stats['end_time']:
            duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
        else:
            duration = (datetime.now() - self.stats['start_time']).total_seconds()

        success_rate = 0
        if self.stats['texts_total'] > 0:
            success_rate = (self.stats['texts_translated'] / self.stats['texts_total']) * 100

        api_success_rate = 0
        if self.stats['api_calls'] > 0:
            api_success_rate = ((self.stats['api_calls'] - self.stats['api_failures']) / self.stats['api_calls']) * 100

        return {
            **self.stats,
            'duration_seconds': duration,
            'success_rate': success_rate,
            'api_success_rate': api_success_rate,
            'cache_hit_rate': (self.stats['texts_cached'] / max(self.stats['texts_total'], 1)) * 100
        }

    def save_report(self, file_path: str):
        """
        保存统计报告

        Args:
            file_path: 报告文件路径
        """
        summary = self.get_summary()

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            logging.error(f"保存统计报告失败: {str(e)}")


def format_duration(seconds: float) -> str:
    """
    格式化时间长度

    Args:
        seconds: 秒数

    Returns:
        格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}小时"


def ensure_directory(path: str):
    """
    确保目录存在

    Args:
        path: 目录路径
    """
    os.makedirs(path, exist_ok=True)
