﻿# Copyright 2004-2022 <PERSON> <<EMAIL>>
#
# Permission is hereby granted, free of charge, to any person
# obtaining a copy of this software and associated documentation files
# (the "Software"), to deal in the Software without restriction,
# including without limitation the rights to use, copy, modify, merge,
# publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so,
# subject to the following conditions:
#
# The above copyright notice and this permission notice shall be
# included in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
# EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
# NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
# LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
# OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
# WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

init python:

    layout.provides('main_menu')

    style.mm_menu_frame = Style(style.menu_frame, help="frame containing main menu")
    style.mm_menu_frame_box = Style(style.vbox, help="box containing main menu buttons")

    style.mm_button = Style(style.button, help="main menu button")
    style.mm_button_text = Style(style.button_text, help="main menu button (text)")

    style.mm_button.size_group = "mm"

    style.mm_menu_frame.xpos = 5.0/6.0
    style.mm_menu_frame.xanchor = 0.5
    style.mm_menu_frame.ypos = 0.9
    style.mm_menu_frame.yanchor = 1.0

label main_menu_screen:

    python hide:

        # Ignore right-click while at the main menu.
        ui.keymap(game_menu=ui.returns(None))

        # Show the background.
        ui.window(style='mm_root')
        ui.null()

        ui.frame(style='mm_menu_frame')
        ui.vbox(style='mm_menu_frame_box')

        for e in config.main_menu:

            if len(e) == 3:
                label, clicked, enabled = e
                shown = "True"
            else:
                label, clicked, enabled, shown = e

            if not eval(shown):
                continue

            # This checks to see if clicked is a string. If so, we want clicked
            # to jump us out of the current context.
            if isinstance(clicked, basestring):
                clicked=ui.jumpsoutofcontext(clicked)

            # Create each button.
            layout.button(label, "mm", enabled=eval(enabled), clicked=clicked)

        ui.close()

        ui.interact(mouse="mainmenu")

    return
