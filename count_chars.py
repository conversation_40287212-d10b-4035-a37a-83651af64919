#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import glob

def count_characters_in_folder(folder_path):
    """
    统计指定文件夹中所有文本文件的字符总数
    
    Args:
        folder_path: 文件夹路径
        
    Returns:
        总字符数、总行数和文件数
    """
    total_chars = 0
    total_lines = 0
    file_count = 0
    
    # 获取文件夹中所有的.txt文件
    txt_files = glob.glob(os.path.join(folder_path, "*.txt"))
    
    # 统计每个文件的字符数
    file_stats = []
    for file_path in txt_files:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.count('\n') + 1
            chars = len(content)
            file_name = os.path.basename(file_path)
            
            file_stats.append((file_name, chars, lines))
            
            total_chars += chars
            total_lines += lines
            file_count += 1
    
    # 按字符数排序
    file_stats.sort(key=lambda x: x[1], reverse=True)
    
    return total_chars, total_lines, file_count, file_stats

def main():
    folder_path = "extracted_quotes"
    
    total_chars, total_lines, file_count, file_stats = count_characters_in_folder(folder_path)
    
    print(f"文件夹: {folder_path}")
    print(f"总文件数: {file_count}")
    print(f"总行数: {total_lines}")
    print(f"总字符数: {total_chars}")
    print(f"估计翻译工作量: 约 {total_chars / 2000:.1f} 千字 (按每千字2000字符计算)")
    
    print("\n按字符数排序的前10个最大文件:")
    for i, (file_name, chars, lines) in enumerate(file_stats[:10]):
        print(f"{i+1}. {file_name}: {chars} 字符, {lines} 行")

if __name__ == "__main__":
    main()
