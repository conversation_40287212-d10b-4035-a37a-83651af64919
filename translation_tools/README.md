# 游戏翻译工具

这个文件夹包含用于翻译游戏文本的工具。

## 文件结构

- `tencent_translator.py` - 使用腾讯云翻译API的翻译脚本
- `deepxl_translator.py` - 使用DeepXL API的翻译脚本
- `tencent_translate.sh` - 腾讯翻译调用脚本（场景1-30）
- `deepxl_translate.sh` - DeepXL翻译调用脚本（场景31-70）
- `start_translation.sh` - 启动所有翻译任务的主脚本
- `logs/` - 日志文件目录

## 使用方法

### 启动所有翻译任务

```bash
cd /Users/<USER>/Downloads/A_Couples_Duet-0.14.5-pc/translation_tools
chmod +x start_translation.sh
./start_translation.sh
```

这将在后台启动两个翻译任务：
- 腾讯翻译任务（场景1-30）
- DeepXL翻译任务（场景31-70）

### 单独启动腾讯翻译任务

```bash
cd /Users/<USER>/Downloads/A_Couples_Duet-0.14.5-pc/translation_tools
chmod +x tencent_translate.sh
nohup ./tencent_translate.sh &
```

### 单独启动DeepXL翻译任务

```bash
cd /Users/<USER>/Downloads/A_Couples_Duet-0.14.5-pc/translation_tools
chmod +x deepxl_translate.sh
nohup ./deepxl_translate.sh &
```

### 查看日志

```bash
# 查看腾讯翻译任务日志
tail -f logs/tencent_translate_*.log

# 查看DeepXL翻译任务日志
tail -f logs/deepxl_translate_*.log
```

## 单独使用翻译脚本

### 腾讯翻译脚本

```bash
python3 tencent_translator.py \
    --secret_id "YOUR_SECRET_ID" \
    --secret_key "YOUR_SECRET_KEY" \
    --input_file "path/to/input.txt" \
    --output_file "path/to/output.rpy" \
    --log_file "path/to/log.log"
```

### DeepXL翻译脚本

```bash
python3 deepxl_translator.py \
    --input_file "path/to/input.txt" \
    --output_file "path/to/output.rpy" \
    --log_file "path/to/log.log"
```

## 注意事项

1. 确保DeepXL服务已在本地启动（端口1188）
2. 腾讯翻译API需要有效的密钥
3. 翻译任务会自动跳过已翻译的文件
4. 日志文件会保存在`logs/`目录下
