#!/bin/bash

# 启动翻译任务的主脚本
# 使用方法: ./start_translation.sh

# 设置工作目录为脚本所在目录
cd "$(dirname "$0")"

# 创建日志目录
mkdir -p logs

# 设置权限
chmod +x tencent_translate.sh
chmod +x deepxl_translate.sh

echo "启动翻译任务..."

# 启动腾讯翻译任务（场景1-30）
echo "启动腾讯翻译任务（场景1-30）..."
nohup ./tencent_translate.sh > logs/tencent_translate_nohup.log 2>&1 &
TENCENT_PID=$!
echo "腾讯翻译任务已启动，PID: $TENCENT_PID"

# 等待5秒，避免两个任务同时启动
echo "等待5秒..."
sleep 5

# 启动DeepXL翻译任务（场景31-70）
echo "启动DeepXL翻译任务（场景31-70）..."
nohup ./deepxl_translate.sh > logs/deepxl_translate_nohup.log 2>&1 &
DEEPXL_PID=$!
echo "DeepXL翻译任务已启动，PID: $DEEPXL_PID"

echo "所有翻译任务已启动！"
echo "腾讯翻译任务 PID: $TENCENT_PID"
echo "DeepXL翻译任务 PID: $DEEPXL_PID"
echo "日志文件位于 logs/ 目录"
echo ""
echo "您可以使用以下命令查看任务状态："
echo "  ps -p $TENCENT_PID,$DEEPXL_PID"
echo "或查看日志文件："
echo "  tail -f logs/tencent_translate_nohup.log"
echo "  tail -f logs/deepxl_translate_nohup.log"
