#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import httpx
import time
import os
import argparse
import logging
from datetime import datetime

# 配置日志
def setup_logger(log_file=None):
    """设置日志记录器"""
    logger = logging.getLogger('deepxl_translator')
    logger.setLevel(logging.INFO)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # 如果提供了日志文件，创建文件处理器
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    return logger

def translate_text(text, source_lang="EN", target_lang="ZH", max_retries=3, retry_delay=2, logger=None):
    """
    使用DeepLX API翻译文本，带重试机制

    Args:
        text: 要翻译的文本
        source_lang: 源语言代码，默认为英文(EN)
        target_lang: 目标语言代码，默认为中文(ZH)
        max_retries: 最大重试次数
        retry_delay: 重试间隔（秒）
        logger: 日志记录器

    Returns:
        包含翻译结果的字典
    """
    log = logger or logging.getLogger('deepxl_translator')
    
    if not text.strip():
        return {
            "success": True,
            "translated_text": "",
            "message": "Empty text",
            "attempts": 0
        }

    data = {
        "text": text,
        "source_lang": source_lang,
        "target_lang": target_lang
    }

    log.info(f"准备翻译文本: {text[:30]}...")

    for attempt in range(max_retries):
        try:
            log.info(f"尝试 {attempt + 1}/{max_retries} 发送请求...")
            response = httpx.post(
                "http://localhost:1188/translate",
                headers={"Content-Type": "application/json"},
                json=data,
                timeout=30
            )

            log.info(f"收到响应状态码: {response.status_code}")

            try:
                result = response.json()
                log.debug(f"响应内容: {result}")
            except Exception as e:
                log.error(f"解析JSON响应失败: {str(e)}")
                log.error(f"原始响应内容: {response.text}")
                result = {"code": 0, "message": f"Failed to parse response: {str(e)}"}

            if result.get("code") == 200:
                translated_text = result.get("data", "")
                log.info(f"翻译成功: {translated_text[:30]}...")
                return {
                    "success": True,
                    "translated_text": translated_text,
                    "message": "Success",
                    "attempts": attempt + 1
                }
            else:
                # 如果不是最后一次尝试，则等待后重试
                if attempt < max_retries - 1:
                    log.warning(f"尝试 {attempt + 1}/{max_retries} 失败: {result.get('message', '未知错误')}，等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                else:
                    return {
                        "success": False,
                        "translated_text": "",
                        "message": f"API错误: {result.get('message', 'Unknown error')} (代码: {result.get('code', 'Unknown')})",
                        "attempts": attempt + 1
                    }
        except Exception as e:
            # 如果不是最后一次尝试，则等待后重试
            if attempt < max_retries - 1:
                log.warning(f"尝试 {attempt + 1}/{max_retries} 异常: {str(e)}，等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                return {
                    "success": False,
                    "translated_text": "",
                    "message": str(e),
                    "attempts": attempt + 1
                }

    # 如果所有重试都失败
    return {
        "success": False,
        "translated_text": "",
        "message": "All retry attempts failed",
        "attempts": max_retries
    }

def translate_file(input_file, output_file, source_lang="EN", target_lang="ZH", logger=None):
    """
    翻译单个文件
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
        source_lang: 源语言代码
        target_lang: 目标语言代码
        logger: 日志记录器
        
    Returns:
        翻译统计信息
    """
    log = logger or logging.getLogger('deepxl_translator')
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    log.info(f"读取源文件: {input_file}")
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.read().splitlines()
    except Exception as e:
        log.error(f"读取文件失败: {str(e)}")
        return {
            "success": False,
            "file": input_file,
            "message": str(e)
        }

    total_lines = len(lines)
    log.info(f"共读取到 {total_lines} 行文本")

    # 翻译每一行
    log.info("\n开始翻译...")
    start_time = time.time()
    translated_results = []
    success_count = 0
    total_attempts = 0

    for i, line in enumerate(lines):
        log.info(f"翻译第 {i+1}/{total_lines} 行...")

        # 跳过空行和特殊行（如音效行）
        if not line.strip() or line.startswith("sounds/"):
            translated_results.append({
                "original": line,
                "translated": line,  # 保持原样
                "success": True,
                "attempts": 0
            })
            success_count += 1
            continue

        result = translate_text(line, source_lang, target_lang, logger=log)

        translated_results.append({
            "original": line,
            "translated": result.get("translated_text", ""),
            "success": result.get("success", False),
            "message": result.get("message", ""),
            "attempts": result.get("attempts", 0)
        })

        if result.get("success"):
            success_count += 1
            if result.get("attempts", 0) > 1:
                log.info(f"成功! (尝试次数: {result.get('attempts')})")
        else:
            log.error(f"失败: {result.get('message', '未知错误')} (尝试次数: {result.get('attempts', 0)})")

        total_attempts += result.get("attempts", 0)

        # 短暂暂停，避免请求过于频繁
        time.sleep(0.5)

    # 计算总耗时和完成度
    elapsed_time = time.time() - start_time
    completion_rate = (success_count / total_lines) * 100 if total_lines > 0 else 0
    avg_attempts = total_attempts / total_lines if total_lines > 0 else 0

    # 生成rpy格式的输出文件
    log.info("\n生成输出文件...")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("translate chinese strings:\n")

        for item in translated_results:
            if item["original"].strip():  # 非空行
                if item["original"].startswith("sounds/"):
                    # 音效行直接保持原样
                    f.write(f"    {item['original']}\n")
                else:
                    # 普通文本行按照格式转换
                    f.write(f'    old "{item["original"]}"\n')
                    if item['success']:
                        f.write(f'    new "{item["translated"]}"\n')
                    else:
                        f.write(f'    new "{item["original"]}"\n')  # 翻译失败时保持原文
                    f.write("\n")
            else:
                # 空行保持原样
                f.write("\n")

    # 打印摘要
    log.info("\n" + "=" * 50)
    log.info(f"翻译完成!")
    log.info(f"成功率: {completion_rate:.2f}% ({success_count}/{total_lines})")
    log.info(f"总耗时: {elapsed_time:.2f} 秒")
    log.info(f"平均尝试次数: {avg_attempts:.2f}")
    log.info(f"结果已保存到: {output_file}")
    log.info("=" * 50)
    
    return {
        "success": True,
        "file": input_file,
        "total_lines": total_lines,
        "success_count": success_count,
        "completion_rate": completion_rate,
        "elapsed_time": elapsed_time,
        "avg_attempts": avg_attempts
    }

def batch_translate(input_folder, output_folder, source_lang="EN", target_lang="ZH", file_pattern=None, logger=None):
    """
    批量翻译文件夹中的文件
    
    Args:
        input_folder: 输入文件夹路径
        output_folder: 输出文件夹路径
        source_lang: 源语言代码
        target_lang: 目标语言代码
        file_pattern: 文件名模式，用于筛选特定文件
        logger: 日志记录器
    """
    log = logger or logging.getLogger('deepxl_translator')
    
    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)
    
    # 获取所有文本文件
    txt_files = []
    for root, _, files in os.walk(input_folder):
        for file in files:
            if file.endswith(".txt"):
                # 如果指定了文件模式，则进行过滤
                if file_pattern and not file.startswith(file_pattern):
                    continue
                txt_files.append(os.path.join(root, file))
    
    if not txt_files:
        log.warning(f"在 {input_folder} 中没有找到匹配的.txt文件")
        return
    
    # 按文件名排序
    txt_files.sort()
    
    log.info(f"将处理 {len(txt_files)} 个文件")
    
    # 准备统计信息
    stats = []
    total_start_time = time.time()
    
    # 处理每个文件
    for file_path in txt_files:
        # 获取文件名（不含路径和后缀）
        file_name = os.path.basename(file_path)
        file_name_without_ext = os.path.splitext(file_name)[0]
        
        # 设置输出文件路径，改为.rpy后缀
        output_file = os.path.join(output_folder, f"{file_name_without_ext}.rpy")
        
        # 检查输出文件是否已存在
        if os.path.exists(output_file):
            log.info(f"跳过已存在的文件: {output_file}")
            continue
        
        # 翻译文件
        result = translate_file(file_path, output_file, source_lang, target_lang, log)
        
        if result["success"]:
            stats.append(result)
        
        log.info("-" * 50)
    
    total_elapsed = time.time() - total_start_time
    
    # 打印总结
    log.info("\n批量翻译总结:")
    log.info(f"总文件数: {len(stats)}")
    log.info(f"总耗时: {total_elapsed:.2f} 秒")
    
    # 保存统计信息到文件
    stats_file = os.path.join(output_folder, "deepxl_translation_stats.json")
    with open(stats_file, 'w', encoding='utf-8') as f:
        import json
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "total_files": len(stats),
            "total_elapsed_seconds": total_elapsed,
            "files": stats
        }, f, indent=2, ensure_ascii=False)
    
    log.info(f"\n统计信息已保存到: {stats_file}")

def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='使用DeepXL API翻译文本文件')
    parser.add_argument('--input_folder', help='输入文件夹路径，用于批量翻译')
    parser.add_argument('--output_folder', help='输出文件夹路径，用于批量翻译')
    parser.add_argument('--input_file', help='输入文件路径，用于单文件翻译')
    parser.add_argument('--output_file', help='输出文件路径，用于单文件翻译')
    parser.add_argument('--source_lang', default='EN', help='源语言代码，默认为英文(EN)')
    parser.add_argument('--target_lang', default='ZH', help='目标语言代码，默认为中文(ZH)')
    parser.add_argument('--file_pattern', help='文件名模式，用于筛选特定文件')
    parser.add_argument('--log_file', help='日志文件路径')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logger(args.log_file)
    
    try:
        # 判断是单文件翻译还是批量翻译
        if args.input_file and args.output_file:
            # 单文件翻译
            translate_file(args.input_file, args.output_file, args.source_lang, args.target_lang, logger)
        elif args.input_folder and args.output_folder:
            # 批量翻译
            batch_translate(args.input_folder, args.output_folder, args.source_lang, args.target_lang, args.file_pattern, logger)
        else:
            logger.error("错误: 必须指定输入/输出文件或输入/输出文件夹")
    except Exception as e:
        logger.error(f"程序异常: {str(e)}")

if __name__ == "__main__":
    main()
