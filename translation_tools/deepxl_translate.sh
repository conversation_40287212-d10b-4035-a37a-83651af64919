#!/bin/bash

# DeepXL翻译调用脚本 - 负责场景31-70的翻译
# 使用方法: nohup ./deepxl_translate.sh &

# 设置工作目录为脚本所在目录
cd "$(dirname "$0")"
cd ..

# 创建日志目录
mkdir -p translation_tools/logs

# 设置日期时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="translation_tools/logs/deepxl_translate_${TIMESTAMP}.log"

# 输入和输出目录
INPUT_DIR="extracted_quotes"
OUTPUT_DIR="game/tl/chinese/scenes"

echo "开始DeepXL翻译任务，日志将保存到: $LOG_FILE"
echo "翻译范围: 场景31-70"

# 检查已翻译的文件
echo "检查已翻译的文件..."
TRANSLATED_FILES=()
for file in "$OUTPUT_DIR"/*.rpy; do
    if [ -f "$file" ]; then
        BASENAME=$(basename "$file" .rpy)
        TRANSLATED_FILES+=("$BASENAME")
    fi
done

echo "已翻译的文件: ${#TRANSLATED_FILES[@]}"
for file in "${TRANSLATED_FILES[@]}"; do
    echo "  - $file"
done

# 获取需要翻译的文件列表
FILES_TO_TRANSLATE=()
for i in $(seq 31 70); do
    # 查找匹配的文件
    for file in "$INPUT_DIR"/$i.*.txt "$INPUT_DIR"/${i}[a-z].*.txt; do
        if [ -f "$file" ]; then
            BASENAME=$(basename "$file" .txt)
            # 检查是否已翻译
            ALREADY_TRANSLATED=false
            for translated in "${TRANSLATED_FILES[@]}"; do
                if [ "$translated" == "$BASENAME" ]; then
                    ALREADY_TRANSLATED=true
                    break
                fi
            done
            
            if [ "$ALREADY_TRANSLATED" = false ]; then
                FILES_TO_TRANSLATE+=("$file")
            fi
        fi
    done
done

echo "需要翻译的文件: ${#FILES_TO_TRANSLATE[@]}"
for file in "${FILES_TO_TRANSLATE[@]}"; do
    echo "  - $file"
done

# 如果没有文件需要翻译，则退出
if [ ${#FILES_TO_TRANSLATE[@]} -eq 0 ]; then
    echo "没有文件需要翻译，退出程序"
    exit 0
fi

# 开始翻译
echo "开始翻译..."
for file in "${FILES_TO_TRANSLATE[@]}"; do
    BASENAME=$(basename "$file" .txt)
    OUTPUT_FILE="$OUTPUT_DIR/$BASENAME.rpy"
    
    echo "翻译文件: $file -> $OUTPUT_FILE"
    python3 translation_tools/deepxl_translator.py \
        --input_file "$file" \
        --output_file "$OUTPUT_FILE" \
        --log_file "$LOG_FILE"
    
    # 添加延迟，避免API限制
    echo "等待5秒..."
    sleep 5
done

echo "翻译任务完成!"
