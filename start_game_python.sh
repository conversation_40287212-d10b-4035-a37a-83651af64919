#!/bin/bash
# 使用系统Python启动Ren'Py游戏

echo "🎮 启动 A Couple's Duet 游戏 (系统Python)"
echo "========================================"

# 检查系统Python
PYTHON_CMD="python3"

if ! command -v "$PYTHON_CMD" &> /dev/null; then
    echo "❌ Python3未找到"
    exit 1
fi

# 检查游戏目录
if [ ! -d "game" ]; then
    echo "❌ 游戏目录不存在"
    exit 1
fi

# 检查翻译文件
TRANSLATION_DIR="game/tl/chinese"
if [ ! -d "$TRANSLATION_DIR" ]; then
    echo "❌ 翻译目录不存在: $TRANSLATION_DIR"
    exit 1
fi

echo "✅ Python: $PYTHON_CMD ($(python3 --version))"
echo "✅ 游戏目录: game/"
echo "✅ 翻译目录: $TRANSLATION_DIR"
echo ""

# 设置环境变量
export PYTHONPATH="lib/python3.9:renpy:$PYTHONPATH"
export RENPY_PLATFORM="linux-x86_64"

# 启动游戏
echo "🚀 启动游戏..."
echo "📋 执行命令: $PYTHON_CMD -O renpy/bootstrap.py ."
echo ""

# 使用系统Python启动游戏
"$PYTHON_CMD" -O renpy/bootstrap.py . 2>&1

EXIT_CODE=$?
echo ""

if [ $EXIT_CODE -eq 0 ]; then
    echo "🎮 游戏正常退出"
else
    echo "❌ 游戏启动失败 (退出代码: $EXIT_CODE)"
    echo ""
    echo "💡 可能的解决方案:"
    echo "1. 安装缺少的Python依赖: pip install pygame"
    echo "2. 检查Ren'Py版本兼容性"
    echo "3. 尝试使用Wine方案"
fi
