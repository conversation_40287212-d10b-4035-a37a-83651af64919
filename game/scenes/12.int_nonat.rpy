label int_nonat:
    $ scene = 12

    scene bg office1 with dissolve

    if (nat_home9 == True) or (nat_mad8 == True):

        show heth upset at ethtalkleft
        show hethclo suit1 at ethtalkleft
        show hethhair 1 at ethtalkleft

        "After the interview, <PERSON> came down to the office lobby with an unhappy expression on his face."
        "From how the interview had gone, he didn't feel good about his chances at getting job."
        "There were many applicants with years of experience with the company applying for that job."
        "<PERSON> wondered why <PERSON> even bothered to send him to an interview he had no chance of passing."

        "<PERSON> felt even more irritated than before."

        "He left the office in a very bad mood."



        jump s12_end




    show heth main at ethtalkleft
    show hethclo suit1 at ethtalkleft
    show hethhair 1 at ethtalkleft

    $ eth_job = True


    "Ethan arrived at the office on time and came to the lobby area after talking with the receptionist."
    "He was feeling nervous as he waited for the interview."


    "After some time, a girl approached <PERSON> with a smile."

    show ria smile at rtalkright
    show rclo office1 at rtalkright
    show riahair 1 at rtalkright

    r "Hi, are you Mr.<PERSON>?"

    show heth happy

    e "Yes, I am."

    r "I'm the project manager <PERSON>'s assistant. She's currently in a very important meeting and unable to meet you."
    r "So <PERSON> asked me to welcome and help you out with the interview."

    if happy_gym8 == True:
        show heth smile
        e "My wife told me about <PERSON>."
    else:




        show heth main
        e "<PERSON>?"

        "<PERSON> looked confused."

        show ria main

        r "<PERSON> told me that your wife had asked her to help you out when you come for the interview."

        e "Oh, <PERSON> did?"

        "Although <PERSON> had been feeling very upset about <PERSON> lying and sneaking out with <PERSON>rel,"
        "When he heard about how <PERSON> had arranged some help for him, he felt a little happy."

        show heth smile

        e "I guess she forgot to tell me."

        show ria smile

        r "That happens sometimes."




    e "I'm sorry for disturbing when all of you are very busy."



    r "It's nothing. I'm happy to help you out."
    r "Nikki had been planning to greet you herself, but she got caught up in a surprise meeting with the higherups."

    e "Well, thank you very much... Uhm..."

    r "I'm Riya."

    e "Thank you very much, Riya."

    r "It's my pleasure, Mr.Ethan."

    e "Please, call me Ethan."

    r "Okay."

    scene bg office2 with dissolve

    show r office1

    show heth smile at ethtalkleft
    show hethclo suit1 at ethtalkleft
    show hethhair 1 at ethtalkleft

    "After the brief greeting, Riya gave Ethan a short tour of the office building as they waited for the interview."

    "It had been a long time since Ethan had worked at an office, but he liked how the place looked."

    "And he liked the attention he got from the ladies in the office."

    "Riya was very pleasant and friendly as she explained everything Ethan wanted to know about the work process and the office."

    "Ethan learned that Riya was an intern fresh off from college."

    "She had an energetic and outgoing personality that matched her youthful appearance."

    hide r office1

    show bg blgr with dissolve
    show r office2 with dissolve:
        zoom 2.0
        yalign 0
        xalign 0.5
        linear 3.0 zoom 0.7

    "Riya had a full set of jet black hair that matched the cute and sharp features of her face."

    "She had a slender build and wasn't as curvaceous and Nat."
    "While Nat's voluptuous body exhumed a primal and potent sexiness, although not as intense and powerful as Nat's, Riya radiated her own elegant and cute sexiness with her slim, tight body."

    "Ethan couldn't help feeling somewhat attracted towards Riya's bright personality and her cuteness."

    hide r office2
    show bg office2 with dissolve

    show ria smile at rtalkright
    show rclo office1 at rtalkright
    show riahair 1 at rtalkright

    r "Okay, Ethan, it's about time they will call you for the interview."

    e "Yes, thank you for showing me around."

    r "Nervous?"

    e "To be honest, yes, I am. But I feel much better than before, thanks to you."

    r "Glad to be of help."
    r "Also, I think Nikki had already made a recommendation on your behalf. So don't worry."

    e "Really? That's very reassuring to hear. I have to thank Nikki later."




    r "I really hope you would get the job."
    r "Having you around the office will definitely be fun and interesting."

    "Ethan felt that there was some underlying meaning in Riya's words and a different look in her eyes as she said that."
    "He felt as if Riya was trying to subtly flirt with him."

    menu:
        "Ethan wondered what his response should be."
        "Flirt back.":
            $ ch12_1eth_flirt = True
            $ unf += 2
            $ eth_flirt12 = True

            show heth smile

            e "I also hope I would get the job."
            e "It would definitely be nice to work with you around."

            "Ethan saw a glint appear in Riya's eyes when he said those words."

            r "Yes, it will be."
        "Keep professional.":


            $ ch12_1eth_profes = True
            $ rom += 2

            show heth smile

            e "I also hope I would get the job."
            e "I really need this right now."

            show ria main

            "Ethan noticed Riya's face changes a bit when he avoided her flirting."

            r "Oh, that's nice to hear."




    r "Let's go. They are waiting."

    e "Okay."

    scene bg office1 with dissolve

    "About an hour later,"

    show heth smile at ethtalkleft
    show hethclo suit1 at ethtalkleft
    show hethhair 1 at ethtalkleft



    show ria smile at rtalkright
    show rclo office1 at rtalkright
    show riahair 1 at rtalkright

    "Riya said her farewells to Ethan at the lobby."

    r "Congratulations on getting the job."

    e "Thank you very much for all the help, Riya. I really appreciate that."


    r "You are welcome, Ethan. It's my pleasure."

    r "I guess I'll see you again very soon."

    e "Yes."

    if eth_flirt12 == True:
        e "I'm looking forward to that."

        r "Me too."

    scene bg office1

    show ria smile at rtalkright
    show rclo office1 at rtalkright
    show riahair 1 at rtalkright

    "Ethan left in a very good mood after saying a few more farewell to Riya."



label s12_end:

pause

jump int_nonat_end
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc
