label s58_jennat_show1:
    $ scene = 59


    if cuck_path == True:

        if f53_qos == False:

            if ch57_jen_job == True:

                scene black with dissolve

                scene bg livingroom1

                show heth happy:

                    zoom 0.8
                    xalign 0.3
                    ypos 75

                show hethclo full5:

                    zoom 0.8
                    xalign 0.3
                    ypos 75

                show hethhair 1:

                    zoom 0.8
                    xalign 0.3
                    ypos 75




                show hnat main:
                    zoom 0.8
                    xalign 0.5
                    ypos 150

                show hn dress2:
                    zoom 0.8
                    xalign 0.5
                    ypos 150


                show hnathair 1:
                    zoom 0.8
                    xalign 0.5
                    ypos 150

                show nbag:
                    zoom 0.8
                    xalign 0.5
                    ypos 150


                show black
                "Before long,"
                hide black with Dissolve(1.0)
                "The fated day for <PERSON> dip her toes into adult entertainment arrived."

                pause

                e "Everything ready?"

                n "Yes, I got some underwear and makeup stuff to take."

                n "<PERSON> said she has everything ready, but I'm taking these just in case."

                e "Nervous?"

                show hnat main1

                n "Of course I am."

                n "Let alone doing something like this, the thought of being on live in front of so many people is nerve wreaking."

                show hnat concerned

                show heth main

                n "Are you sure we'll be fine, <PERSON>?"

                n "I'm getting cold feet now."

                "<PERSON> said in worry, showing her nervousness."

                show heth happy

                e "Yeah, it's natural for you to feel so nervous."

                e "Even I'm feeling so nervous."

                e "But don't worry, babe."

                e "We've discussed about this so many times."

                e "We have taken precautions and planned everything."

                e "Today, you'll be just testing the waters."

                e "And Jenna will be there with you too."

                e "If you don't feel like this is the best option for you after today, we can stop this."

                e "So, please don't worry too much, Nat."

                pause

                show hnat happy

                "Ethan's words never failed to calm Nat's nerves."

                "This was the third time Ethan had to calm her nerves today."

                n "Okay."

                n "I'm sorry, I'm just being a nervous wreak right now."

                e "As I said, it's natural for you to be nervous in this situation."

                n "I feel better now."

                n "Let's go over to Jen's place before I get cold feet again."

                show heth smile

                e "Yeah, that's a good idea."

                pause

                e "Babe."

                n "Yeah?"

                show heth content

                e "Can't you atleast give me a hint about what you'll be wearing?"

                show hnat smile1

                n "Be patient, honey."

                n "Didn't we already discussed that keeping this a surprise for you will be the most fun way?"

                e "I know, but I can't help getting impatient."

                show hnat smile

                n "Looks like it's already working on you."

                "Nat said with a chuckle."

                show heth smile

                e "You know me the best."

                n "Okay, let's go."

                e "Yeah."









                pause


    elif nat_reluc_path == True:
        if ch57_jen_job == True:
            pause



    elif vanilla_path == True:
        if ch57_jen_job == True:
            pause

    elif ntr_path == True:

        if nat_leave38 == True:
            pause

        elif eth_obli38 == True:
            if ch57_jen_job == True:
                pause

        elif f51_ntr == True:
            if ch57_jen_job == True:
                pause

        elif f51_hide == True:
            if ch57_jen_job == True:
                pause
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc
