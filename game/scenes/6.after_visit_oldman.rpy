label after_visit_oldman:

    if jel > 2:
        $ dar_upset1 = True









    scene bg kitchen with fade

    show nat main at ntalkleft
    show n dress1 at ntalkleft
    show nathair at ntalkleft

    show eth serious at ethtalkright
    show ethpants1 at ethtalkright
    show ethc<PERSON> whitet at ethtalkright


    jump notest
    nt "hello"
    et "hi."
    nt "nothing"
    et "go."
    nt "If <PERSON> gets mad and starts looking for reasons to throw us out, we can become homeless."
    et "It's not overreacting to be mad when I find another man groping my wife's ass."
    nvl show hpunch
    show phone dia
    show natkitch at center with dissolve:

    pause
    nt "goodbye"
    et "hi."
    nt "hello"
    et "hi."
    label notest:

    if insult_pet5 == True:

        show nat upset with dissolve
        n "Why did you have to overreact like that, Ethan?"
        e "It's not overreacting to be mad when I find another man groping my wife's ass."
        n "Did you forget about the situation we are in right now?"
        n "If <PERSON> gets mad and starts looking for reasons to throw us out, we can become homeless."
        n "We have already spent the rest of our money on this move."
        e "Then what should I have done?"
        n "I was the one getting harassed, and I was bearing with it for our sake."
        n "I was waiting for a chance to brush him off without insulting him. I had things under control."

        if eth_upset5 == False:
            e "You might think you had things under control. But I can't just ignore a dirty old man touching my woman or her allowing it to happen."
            jump s6jump1
        show eth irritated with dissolve
        e "Well, after seeing you teasing that old man like some skank, I really doubt you were looking for a chance to brush him off."
        show nat shocked with dissolve
        pause
        if catch_eth1 == True:
            show nat angry with dissolve
            n "Well, you didn't think that it was a big deal to ogle at other women right in front of me."
            n "I did that to show you how I felt. And it looks like you didn't like it either."
            e "With an old man? You are disgusting."
            e "Now I feel like you were encouraging him to grab your ass."

        label s6jump1:



        jump s6argue
    else:

        if eth_upset5 == True:

            "Although Ethan had stopped things and rescued Nat from Peter's harassment, the fact that she might have intentionally teased Peter was gnawing in Ethan's mind."

            menu:
                "Confront Nat about her showing too much to Peter.":
                    $ ch6_1eth_confront = True
                    $ jel += 2
                    $ ang += 2

                    show eth irritated with dissolve

                    "Nat, why were you showing Peter too much? Even if you weren't aware, you should know better to be careful when wearing short dresses."
                    if catch_eth1 == True:
                        show nat angry with dissolve
                        n "Well, you didn't think it was a big deal to ogle at other women right in front of me."
                        n "I did that to show you how I felt. And it looks like you didn't like it either."
                        e "With an old man? You are disgusting."
                        e "Now I feel like you were encouraging him to grab your ass."

                    jump s6argue
                "Avoid talking about it.":
                    $ ch6_1eth_avoid = True
                    "Although it was troubling Ethan, he chose not to broach the topic, wanting to avoid an argument."
                    jump s6avoid_topic
        else:


            if nat_show5 == True:
                if catch_eth5 == True:
                    if pet > 1:
                        show nat concerned with dissolve
                        show eth emo with dissolve
                        "Nat felt confused from finding out that Ethan had been silently watching Nat getting groped by Peter without coming to her aid."
                        "Nat felt even more confused about the strange enjoyment she had experienced from getting groped by old Peter."
                        "Although Nat wanted to ask Ethan for a reason for his behavior, she felt too guilty to bring up the topic."
                        jump s6avoid_topic
                    else:

                        show nat concerned with dissolve
                        show eth emo with dissolve
                        "Nat partly blamed herself for unwisely teasing Peter and attracting his unwanted attention."
                        "But what troubled her the most was finding out that Ethan had been silently watching her getting groped by Peter without coming to her aid."
                        "Nat tried to bring it up to discussion to ask Ethan for a reason, but he was avoiding her."
                        jump s6avoid_topic
                else:
                    if save_nat5 == True:

                        show nat main with dissolve
                        show eth happy with dissolve
                        "Nat felt happy and loved after being rescued from Peter by Ethan."
                        "She was feeling silly about teasing an old man in that manner and attracting unwanted attention and trouble."
                        jump s6happy
                    else:
                        if pet > 2:

                            $ nateth_conflicted6 = True
                            show nat embarrased with dissolve
                            show eth happy with dissolve
                            "As a married woman, Nat was feeling very guilty and conflicted."
                            "Nat couldn't believe she had enjoyed her sexual harassment at Peter's hand."
                            "She couldn't figure out where those strange feelings had come from and why she had allowed them to affect her."
                            jump s6avoid_topic
                        else:

                            show nat embarrased with dissolve
                            show eth happy with dissolve
                            "Nat regretted allowing her emotions to tease Peter."
                            "It had encouraged Peter to put his hands on her."
                            "She felt foolish, guilty, and awkward to face Ethan now."
                            "Nat's only solace was she believed Ethan did not know the blunder she had made."
                            jump s6avoid_topic
            else:
                if catch_eth5 == True:

                    show nat upset with dissolve
                    show eth sad with dissolve
                    "Nat was seething about Ethan had just watched her getting molested by an old pervert without coming to her aid."
                    n "Ethan, why didn't you help me?"
                    e "I thought you had it under control."
                    show nat angry with dissolve
                    n "You thought things were under control when another man had his filthy hands groping my bare ass?"
                    e "I... I thought you were enjoying."

                    $ argue6_check = 1
                    jump s6argue
                else:
                    if save_nat5 == True:

                        show nat bigsmile with dissolve
                        show eth happy with dissolve
                        "Nat was feeling very happy about Ethan saving her from Peter."
                        n "Thank you for saving me there, Ethan. You were my knight in the shining armour."
                        e "I was just doing my job as your husband."
                        n "Well, you still deserve a big reward."
                        show eth bigsmile with dissolve

                        e "Ooh, what is this reward?"
                        show nat lipbitebrow with dissolve
                        n "You'll see later."
                        "After giving Ethan a sexy look, Nat walked away."
                        $ nat_treat6 = True
                        jump s6happy
                    else:

                        show nat main with dissolve
                        show eth happy with dissolve

                        e "Peter is a very nice and chill guy. What do you think?"
                        n "He is nice and chill, but our landlord was trying to get a bit touchy with me in the kitchen."

                        show eth surprise with dissolve

                        e "Really? What happened?"

                        n "Nothing, I just brushed him off without making a scene. We can't afford to upset him too much."

                        show eth happy with dissolve

                        e "Yeah. I know you are smart and will always make the right call in these kind of situations."

                        show nat bigsmile with dissolve

                        n "I think I deserve a reward for this smooth save."

                        show eth bigsmile with dissolve

                        e "Oh, don't worry. I'll give you more than one reward tonight."

                        show nat lipbitebrow with dissolve

                        n "I'll look forward to it then."

                        $ eth_treat6 = True

                        jump s6happy





    label s6argue:
        $ nateth_argue6 = True
        n "Ethan!"
        "Nat stared at Ethan with tears welling in her eyes."

        hide nat
        hide n
        hide nathair

        show eth serious with dissolve

        "Without saying anymore, she walked away, wiping her tears."
        "With a complex look in his eyes, Ethan watched as Nat walked away."

        scene black
        scene bg bedroom1 with dissolve

        if argue6_check == 1:
            show eth concerned at ethtalkright
        else:
            show eth irritated at ethtalkright



        show ethclo boxers at ethtalkright


        show nat upset at ntalkleft
        show n babydoll at ntalkleft
        show nathair 1 at ntalkleft

        pause

        "There was visible tension between Nat and Ethan when they came to the bedroom that night."
        "After the argument, they had avoided each other."

        if argue6_check == 1:
            e "Uhmm... Nat, do you want to?"
            n "I'm not in the mood for anything."

            "Nat cut Ethan off before he could say anything else and went to bed."

        scene black

        show nateth bed6

        "Nat and Ethan went to sleep that night without even exchaning 'goodnights.'"



        jump s6end
    label s6avoid_topic:
        scene black
        scene bg bedroom1 with dissolve

        show eth concerned at ethtalkright

        show ethclo boxers at ethtalkright


        show nat embarrased at ntalkleft
        show n babydoll at ntalkleft
        show nathair 1 at ntalkleft

        "That night, when Ethan and Nat arrived at the bedroom, there was a strange awkwardness in the atmosphere."
        "After their visit to Peter, they had talked less and been avoiding talking about Peter or the visit at all."

        n "Uhm, goodnight, honey."

        e "Oh, goodnight, baby."

        scene black

        show nateth bed6

        "Nat and Ethan went to sleep without sharing any more affectionate words or actions like they usually do."

        pause

        jump s6end


    label s6happy:
        $ nateth_happy6 = True
        scene black
        scene bg bedroom1 with dissolve



        if nat_treat6 == True:
            show eth happy at ethtalkleft

            show ethclo under1 at ethtalkleft

            "That night, Ethan eagerly waited for Nat to finish her shower and join him in the bedroom."

            show nat main at ntalkright
            show n towel at ntalkright
            show nathair 1 at ntalkright

            "After some time, Nat came to the bedroom with a towel wrapped around her curvy body."
            "There was something uniquely sexy about a freshly showered beautiful woman wrapped in a small towel."
            "The slight dampness on her hair and smooth skin added to her already mesmerizing sexiness."

            show nat bigsmilebrowup with dissolve

            n "So, are you ready for your reward, handsome?"

            label nat_treat6jump:

            show eth bigsmile with dissolve

            e "Hell yeah, baby."

            show nat bigsmile with dissolve

            hide ethclo
            show ethpenis flacid at ethtalkleft

            "Ethan quickly and eagerly stripped off his underwear."



            hide nat
            hide n
            hide nathair

            show natholdtowel 1 at ntalkrightright with dissolve:
                zoom 1.2

            "Nat walked near to the bed and started to unwrap the towel from her body in a teasing manner."
            show natholdtowel 2 at ntalkrightright with dissolve:
                zoom 1.2
            pause (1)

            show natholdtowel 1 at ntalkrightright with dissolve:
                zoom 1.2
            pause (1)

            show natholdtowel at ntalkrightright with dissolve:
                zoom 1.2

            show ethpenis erect with dissolve

            "By the time nat held the towel off her body and revealed the fully glory of her naked beauty to her husband, Ethan was already erect due to her erotic performance."

            hide natholdtowel

            show nathold towel at ntalkrightright:
                zoom 1.2
            show towel1 at ntalkrightright:
                zoom 1.2

            n "Now, get your hard cock over here for your reward."

            "With those sexy words, Nat let the towel drop to the floor."

            show towel1:


                linear 1.00 ypos 1900

            pause (1)

            e "Yes, Maam!"

            "Ethan was very eager to recieve his reward as he did what he was told."


            jump treatbj6
            label treatbj6_end:

            "After enjoying Nat's mouth for a while."

            e "Let me give you a little reward in return, baby."

            jump treatfinger6
            label treatfinger6_end:

            jump treatend6

        if eth_treat6 == True:

            show eth happy at ethtalkleft

            show ethclo under1 at ethtalkleft

            "That night, Ethan eagerly waited for Nat to finish her shower and join him in the bedroom."

            show nat main at ntalkright
            show n towel at ntalkright
            show nathair 1 at ntalkright

            "After some time, Nat came to the bedroom with a towel wrapped around her voluptuous body."
            "There was something uniquely sexy about a freshly showered beautiful woman wrapped in a small towel."
            "The slight dampness on her hair and smooth skin added to her already mesmerizing sexiness."

            show eth bigsmile with dissolve

            e "So, are you ready for your reward, baby?"

            show nat bigsmilebrowup with dissolve

            n "How's this for an answer?"

            hide nat
            hide n
            hide nathair

            show natholdtowel 2 at ntalkright with dissolve:
                zoom 1.3

            show natholdtowel at ntalkright with dissolve:
                zoom 1.3

            hide natholdtowel

            show nathold towel at ntalkright:
                zoom 1.3
            show towel1 at ntalkright:
                zoom 1.3

            "Nat unwrapped the towel from her body and revealed the fully glory of her naked beauty to her husband."

            e "Oh, look who's eager for her reward."

            hide ethclo
            show ethpenis erect at ethtalkleft

            "Ethan said teasingly as he took off his underwear and revealed his already erect manhood."


            n "Looks like I'm not the only one eager here."

            show towel1:


                linear 1.00 ypos 1900

            pause (1)

            "Nat said while letting the towel in her hand fall to the floor."



            "And looked desirously at Ethan's erection."

            e "You'll get this big reward later, but you'll get something else first."

            "Ethan said as he led Nat towards the bed."

            $ treat_check6 = 1

            jump treatfinger6

            label treatfinger6_end1:

            "Nat looked very statisfied Ethan's special reward and the orgasm she had experienced."

            n "Now it's my turn to give you a little reward in return."

            jump treatbj6
            label treatbj6_end1:

            jump treatend6



        show eth happy at ethtalkleft

        show ethclo under1 at ethtalkleft



        show nat main at ntalkright
        show n towel at ntalkright
        show nathair 1 at ntalkright

        "That night, Nat came to the bedroom with a towel wrapped around her curvy body."
        "There was something uniquely sexy about a freshly showered beautiful woman wrapped in a small towel."
        "The slight dampness on her hair and smooth skin added to her already mesmerizing sexiness."

        "Nat was feeling a some guilt about her actions in the evening and decided to treat her husband for his actions."

        show nat bigsmilebrowup with dissolve

        n "Are you ready for a treat, handsome?"

        jump nat_treat6jump


        label treatbj6:
            scene black with fade
            show bg bedroom1:
                zoom 1.2
                yalign 1.2

            show natethblj a1

            "Nat knelt in front of Ethan and took his hard memeber into her welcoming mouth."

            e "Ooh!"

            "Ethan moaned in pleasure and held onto Nat's head as she started moving her head."

            hide natethblj a1

            play sound "sounds/blowjob1.mp3"

            show natethblj6ani

            "Ethan started moaning loud as he enjoyed being orally pleasured by his wife."

            "Encouraged by the expression on Ethan's face, his moans and the way his hands pulled on her head, Nat used her skills to bring him even more pleasure."

            pause





            stop sound


            if treat_check6 == 1:
                jump treatbj6_end1

            jump treatbj6_end



        label treatfinger6:

            scene black with dissolve
            show natethbed1
            show natethfinger a1

            "Ethan took Nat to the bed and began to finger her while kissing her neck."

            hide natethfinger a1

            show natethfinger6ani

            play sound "sounds/finger1.mp3"

            pause

            "And before long, Nat starting climaxing due to Ethan's experience and knowledge about where her pleasure points were and how to push them right."

            play sound "sounds/orgasm5.mp3"

            pause


            stop sound

            hide natethfinger6ani

            show natethfinger a1

            if treat_check6 == 1:
                jump treatfinger6_end1

            jump treatfinger6_end


        label treatend6:

            scene black with dissolve

            show bg bedroom1:
                zoom 2.0
                xalign 0.7
                ypos -350

            show natethride a1

            "With a lustful fire in her eyes, Nat pushed Ethan to lie back on the bed and climbed atop him before impaling herself on his hard cock."

            hide natethride a1
            show natethride6ani

            play sound "sounds/lovemaking2.mp3"

            "Nat rode Ethan while giving Ethan a great show of her sexy body rolling in waves."
            "She even had to hold her big breasts down to keep them from moving around too much."

            pause

            "Given how sexy Nat looked and how well she rode on top, as usual, Ethan couldn't last too long and started orgasming."


            play sound "sounds/morgasm1.mp3"

            pause

            hide natethride6ani
            show natethride a4



            stop sound

            scene black with dissolve
            show nateth sleep6

            "After the passionate lovemaking, Nat and Ethan felt satisfied and exhausted."
            "They went to sleep in nude while holding each other, feeling closer and more loved than ever."












label s6end:



pause

jump after_visit_oldman_end
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc
